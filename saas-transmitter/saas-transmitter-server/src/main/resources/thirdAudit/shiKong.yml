dslType: contract
enable: true
name: 处方信息上送
format: json
common: false
domain: business.data['network']['networkItem']['url']
functions:
  - path: "'/jnsk/PescriptionServlet'"
    bodyStrategy: jsonNode
    request:
      method: POST
      header:
        "Content-Type": "'application/json'"
      body:
        "apply_id": business.data['data']['inquiryPref'] # 问诊编码
        "patient_name": business.data['data']['patientName'] # 患者姓名
        "sex": business.data['data']['patientSex'] # 患者性别
        "birthday": "T(org.apache.commons.lang3.StringUtils).isNotEmpty(business.data['aux']['inquiryInfo']['patientAge']) ? T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd').format(T(java.time.LocalDate).now().minusYears(T(java.lang.Integer).parseInt(business.data['aux']['inquiryInfo']['patientAge']))) : null"
        "telephone": business.data['data']['patientMobile'] # 患者手机号
        "married": "'0'" # 是否结婚 0-未知 1-未婚 2-已婚
        "allergy": "T(cn.hutool.core.collection.CollUtil).isEmpty(business.data['data']['allergic']) ? '0' : '1'" # 过敏史标志
        "doctor_name": business.data['data']['doctorName'] # 开方医生
        "departments": "'内科'"
        "hospital": "'海南宜贰叁互联网医院'"
        "diagnostic_content": T(org.apache.commons.lang3.StringUtils).joinWith('|',business.data['data']['diagnosisName'])
        "create_time": "business.data['data']['outPrescriptionTime'].atZone(T(java.time.ZoneId).of('Asia/Shanghai')).toInstant().toEpochMilli()" # 开方日期
        "prescription_url": business.data['data']['prescriptionImgUrl']
        "approval_user": "''" # 审核药师姓名
        "approval_time": "'0'" # 处方审核时间,毫秒级时间戳
        "drugstore_account": "business.data['data']['tenantInfo']['ext'] != null && T(org.apache.commons.lang3.StringUtils).isNotEmpty(business.data['data']['tenantInfo']['ext']['organSign']) ? business.data['data']['tenantInfo']['ext']['organSign'] : business.data['data']['tenantInfo']['pref']"
        "approval_status": "'0'"
        "prescription_code": business.data['data']['pref']
        "drug_info":
          - "drug_name": business.data['aux']['prescriptionDetail'][_index_]['commonName'] # 对应处方签中药品名称
            "drug_code": "'10086'"
            "quantity": business.data['aux']['prescriptionDetail'][_index_]['quantity'].intValue() # 对应处方签中该药品的数量
            "unit": business.data['aux']['prescriptionDetail'][_index_]['packageUnit'] # 对应处方签中该药品的单位
            "unit2": business.data['aux']['prescriptionDetail'][_index_]['singleUnit']
            "specification": business.data['aux']['prescriptionDetail'][_index_]['attributeSpecification'] # 对应处方签中该药品的规格
            "unit_price": "'0'"
            "usage": business.data['aux']['prescriptionDetail'][_index_]['directions']
            "dosage_info": "business.data['aux']['prescriptionDetail'][_index_]['singleDose'] + business.data['aux']['prescriptionDetail'][_index_]['singleUnit']"
            "frequency": business.data['aux']['prescriptionDetail'][_index_]['useFrequency']
    response:
      # 根据实际API响应结构调整
      "success": "['code'] == '200'"
      "message": "['msg']"
      "code": "['code'] == '200' ? 0 : 1"

