dslType: contract
enable: true
name: 处方信息上送
format: json
common: false
domain: business.data['network']['networkItem']['url']
functions:
  - path: "'/ZyApi/getBxtOrder'"
    bodyStrategy: jsonNode
    request:
      method: POST
      header:
        "Content-Type": "'application/json'"
      body:
        "signatureCoordinates": "'402,710'" # 处方坐标
        "prescriptionId": business.data['data']['pref'] # 处方号
        "applySource": "'0'"
        "doctorAuditTime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd").format(business.data['data']['outPrescriptionTime']) # 开方时间，格式"2021-01-0110:00:00"
        "doctorName": business.data['data']['doctorName'] # 开方医生
        "doctorTitle": business.data['aux']['doctorInfo']?.titleName
        "doctorHospitalName": "'成都双流宜贰叁互联网医院'"
        "doctorDeptName": business.data['aux']['doctorInfo']?.firstPracticeDeptName
        "internetDoctorHospital": "'成都双流宜贰叁互联网医院'"
        "illness": T(org.apache.commons.lang3.StringUtils).joinWith('|',business.data['data']['diagnosisName'])
        "symptom": "T(org.apache.commons.lang3.StringUtils).joinWith(',',business.data['data']['mainSuit'])"  # 疾病病情描述
        "storeNo": business.data['aux']['qinDouYuNuoThirdTenantCode']
        "storeName": business.data['data']['tenantInfo']['name']
        "prescriptionURL": business.data['data']['prescriptionImgUrl']
        "orderType": "business.data['data']['medicineType'] == 0 ? '1' : '2'" # 用药类型 1-西药 2-中药
        "patientInfo":
          "patientName": business.data['data']['patientName'] # 患者姓名
          "patientGender": "business.data['data']['patientSex'] != null && business.data['data']['patientSex'] == '1' ? '1' : '0'"
          "patientAge": "(T(java.lang.Integer).parseInt(business.data['aux']['inquiryDetailInfo']?.patientAge))" # 患者年龄
          "patientCardId": business.data['aux']['inquiryDetailInfo']?.patientIdCard
          "mobile": business.data['aux']['inquiryDetailInfo']?.patientMobile # 患者手机号
        "drugDetailList":
          - "drugName": business.data['aux']['prescriptionDetail'][_index_]['commonName']
            "perNum": business.data['aux']['prescriptionDetail'][_index_]['singleDose']
            "perUnit": business.data['aux']['prescriptionDetail'][_index_]['singleUnit']
            "bizId": business.data['aux']['prescriptionDetail'][_index_]['standardId']
            "spec": business.data['aux']['prescriptionDetail'][_index_]['attributeSpecification'] # 规格
            "usage": business.data['aux']['prescriptionDetail'][_index_]['directions']
            "num": business.data['aux']['prescriptionDetail'][_index_]['quantity'].intValue()
            "unit": business.data['aux']['prescriptionDetail'][_index_]['packageUnit'] # 对应处方签中该药品的单位
            "dose": business.data['aux']['prescriptionDetail'][_index_]['manufacturer']
            "usageFrequencyName": business.data['aux']['prescriptionDetail'][_index_]['useFrequency']
            "barcode": business.data['aux']['prescriptionDetail'][_index_]['barcode']
            "drugNo": business.data['aux']['prescriptionDetail'][_index_]['approvalNumber']
    response:
      # 根据实际API响应结构调整
      "success": "['ok']"
      "message": "['message']"
      "code": "['ok'] ? 0 : 1"
