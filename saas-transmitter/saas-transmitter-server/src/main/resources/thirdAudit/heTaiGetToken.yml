name: '获取token'
domain: http://116.62.50.244:8004
path: /v1/auth
common: false
dslType: contract
enable: true
protocol: http
format: json
functions:
  - request:
      method: POST
      format: form
      body:
        "tokenKey": "'adjduehdhs27272sss'"
    response:
      # 根据实际API响应结构调整
      "success": "['code'] == '200'"
      "code": "['code'] == '200' && ['data']['isok' ? 0 : 1"
      "accessToken": "['data']['token']" #token