dslType: contract
enable: true
name: 处方信息上送
format: json
common: false
domain: "'http://sfpt.eyao168.com:8088'"
functions:
  - path: "'/jnsk/PescriptionServlet'"
    bodyStrategy: jsonNode
    request:
      method: POST
      header:
        "Content-Type": "'application/json'"
      body:
        "insNo": "business.data['data']['tenantInfo']['ext'] != null && T(org.apache.commons.lang3.StringUtils).isNotEmpty(business.data['data']['tenantInfo']['ext']['organSign']) ? business.data['data']['tenantInfo']['ext']['organSign'] : business.data['data']['tenantInfo']['pref']"
        "cfNo": business.data['data']['pref'] # 处方号
        "hospitalName": "'海南宜贰叁互联网医院'"
        "createTime": "business.data['data']['inquiryStartTime'].atZone(T(java.time.ZoneId).of('Asia/Shanghai')).toInstant().toEpochMilli()"
        "userName": business.data['data']['patientName'] # 患者姓名
        "idCardNo": business.data['aux']['inquiryDetailInfo']?.patientIdCard # 患者身份证
        "sex": business.data['data']['patientSex'] # 患者性别
        "age":  (T(java.lang.Integer).parseInt(business.data['aux']['inquiryDetailInfo']?.patientAge)) # 患者年龄
        "phone": business.data['aux']['inquiryDetailInfo']?.patientMobile # 患者手机号
        "zdCont": T(org.apache.commons.lang3.StringUtils).joinWith('|',business.data['data']['diagnosisName']) # 诊断
        "doctorName": business.data['data']['doctorName'] # 开方医生
        "medicineType": business.data['data']['medicineType'] # 用药类型 0-西药 1-中药
        "docDeptName": "business.data['data']['medicineType'] != null && business.data['data']['medicineType'] == 0 ? '内科' : '中医科'"
        "doctorTime": "business.data['data']['outPrescriptionTime'].atZone(T(java.time.ZoneId).of('Asia/Shanghai')).toInstant().toEpochMilli()"
        "cfImgUrl": business.data['data']['prescriptionImgUrl']
        "wareList":
          - "wareId": business.data['aux']['prescriptionDetail'][_index_]['standardId'] # 药品编码
            "medicinesQuasiName": business.data['aux']['prescriptionDetail'][_index_]['approvalNumber'] # 国药准字号
            "wareQty": business.data['aux']['prescriptionDetail'][_index_]['quantity'].intValue() # 对应处方签中该药品的数量
            "drugName": business.data['aux']['prescriptionDetail'][_index_]['commonName'] # 药品名称
            "productUnitId": "'0'"
            "singleUnit": business.data['aux']['prescriptionDetail'][_index_]['singleUnit'] # 剂量单位
            "specification": business.data['aux']['prescriptionDetail'][_index_]['attributeSpecification'] # 规格
            "usage": business.data['aux']['prescriptionDetail'][_index_]['directions'] # 用法
            "dosageInfo": "business.data['aux']['prescriptionDetail'][_index_]['singleDose'] + business.data['aux']['prescriptionDetail'][_index_]['singleUnit']"
            "frequency": business.data['aux']['prescriptionDetail'][_index_]['useFrequency']
        "tcmPrescriptionDetailList":
          - "wareId": business.data['aux']['prescriptionDetail'][_index_]['standardId'] # 药品编码
            "wareQty": business.data['aux']['prescriptionDetail'][_index_]['quantity'].intValue() # 对应处方签中该药品的数量
            "drugName": business.data['aux']['prescriptionDetail'][_index_]['commonName'] # 药品名称
            "medicinesPackageUtil": "'0'"
            "specification": business.data['aux']['prescriptionDetail'][_index_]['attributeSpecification'] # 规格
        "tcmInstructionList":
          - "tcmUsage": business.data['data']['ext']['tcmDirections'] # 中药用法
            "tcmDailyDosage": # 中药每日副数
            "tcmDailyFrequency": # 中药每日频次
            "tcmProcessingMethod": business.data['data']['ext']['tcmProcessingMethod'] # 中药加工方式
            "tcmTotalDosage": business.data['data']['ext']['tcmTotalDosage'] # 中药总副数
    response:
      # 根据实际API响应结构调整
      "success": "['success']"
      "message": "['message']"
      "code": "['code']"

