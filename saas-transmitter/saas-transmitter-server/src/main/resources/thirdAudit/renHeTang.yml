dslType: contract
enable: true
name: 处方信息上送
format: json
common: false
domain: "'http://sfpt.eyao168.com:8088'"
functions:
  - path: "'/jnsk/PescriptionServlet'"
    bodyStrategy: jsonNode
    request:
      method: POST
      header:
        "Content-Type": "'application/json'"
      body:
        "no": business.data['data']['pref'] # 处方号
        "memberName": business.data['data']['patientName'] # 患者姓名
        "memberSex": "business.data['data']['patientSex'] != null && business.data['data']['patientSex'] == '1' ? '1' : '0'"
        "memberAge": "(T(java.lang.Integer).parseInt(business.data['aux']['inquiryDetailInfo']?.patientAge))" # 患者年龄
        "memberCard": business.data['aux']['inquiryDetailInfo']?.patientIdCard
        "memberTel": business.data['aux']['inquiryDetailInfo']?.patientMobile
        "hospitalName": "'海南宜贰叁互联网医院'"
        "doctorName": business.data['data']['doctorName'] # 开方医生
        "squareTime": "business.data['data']['outPrescriptionTime'].atZone(T(java.time.ZoneId).of('Asia/Shanghai')).toInstant().toEpochMilli()"
        "imgOne": business.data['data']['prescriptionImgUrl']
        "storeCode": "business.data['data']['tenantInfo']['ext'] != null && T(org.apache.commons.lang3.StringUtils).isNotEmpty(business.data['data']['tenantInfo']['ext']['organSign']) ? business.data['data']['tenantInfo']['ext']['organSign'] : business.data['data']['tenantInfo']['pref']"
        "storeName": business.data['data']['tenantInfo']['name']
        "saleNo": "''"
        "drugList":
          - "drugName": business.data['aux']['prescriptionDetail'][_index_]['commonName'] # 药品名称
            "drugSpec": business.data['aux']['prescriptionDetail'][_index_]['attributeSpecification'] # 规格
            "drugUnit": business.data['aux']['prescriptionDetail'][_index_]['packageUnit'] # 药品单位
            "drugNumber": business.data['aux']['prescriptionDetail'][_index_]['quantity'].intValue()
            "drugType": "'0'"
            "usage": business.data['aux']['prescriptionDetail'][_index_]['directions']
            "dosage": "'一次' + business.data['aux']['prescriptionDetail'][_index_]['singleDose'] + business.data['aux']['prescriptionDetail'][_index_]['singleUnit'] + ',' + business.data['aux']['prescriptionDetail'][_index_]['useFrequency']"
            "drugCode": business.data['aux']['prescriptionDetail'][_index_]['standardId']
    response:
      # 根据实际API响应结构调整
      "success": "['success']"
      "message": "['message']"
      "code": "['code']"

