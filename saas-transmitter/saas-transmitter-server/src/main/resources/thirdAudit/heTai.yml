dslType: contract
enable: true
name: 处方信息上送
format: json
common: false
inputParamConvertConcreteType: true
domain: business.data['network']['networkItem']['url']
functions:
  - path: "'/v1'"
    bodyStrategy: jsonNode
    request:
      method: POST
      header:
        "Content-Type": "'application/json'"
      body:
        "code": business.data['data']['pref'] # 处方号
        "doctorName": business.data['data']['doctorName'] # 开方医生
        "auditDate": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(business.data['data']['outPrescriptionTime']) # 开方日期
        "auditStatus": true
        "imgSourceUrl": business.data['data']['prescriptionImgUrl']
        "patientName": business.data['data']['patientName'] # 患者姓名
        "patientSex": "business.data['data']['patientSex'] != null && business.data['data']['patientSex'] == '1' ? 'M' : 'F'"
        "patientIdCard": business.data['aux']['inquiryDetailInfo']?.patientIdCard # 患者身份证
        "drugStoreId": "business.data['data']['tenantInfo']['ext'] != null && T(org.apache.commons.lang3.StringUtils).isNotEmpty(business.data['data']['tenantInfo']['ext']['organSign']) ? business.data['data']['tenantInfo']['ext']['organSign'] : business.data['data']['tenantInfo']['pref']"
        "recipelType": "business.data['data']['medicineType'] != null && business.data['data']['medicineType'] == '0' ? 'W' : 'Z'"
        "isSelfRecipel": false
        "details":
          - "name": business.data['aux']['prescriptionDetail'][_index_]['commonName'] # 对应处方签中药品名称
            "spec": business.data['aux']['prescriptionDetail'][_index_]['attributeSpecification'] # 对应处方签中该药品的规格
            "isOct": T(java.util.Objects).equals(business.data['aux']['prescriptionDetail'][_index_]['presCategory'],'处方药')
            "packUnitName": business.data['aux']['prescriptionDetail'][_index_]['packageUnit'] # 对应处方签中该药品的单位
            "quantity": business.data['aux']['prescriptionDetail'][_index_]['quantity'].intValue() # 对应处方签中该药品的数量
            "productPref": business.data['aux']['prescriptionDetail'][_index_]['standardId']
    response:
      # 根据实际API响应结构调整
      "success": "['code'] == 200 && ['data']['isok']"
      "message": "['msg']"
      "code": "['code'] == 200 && ['data']['isok'] ? 0 : 1"