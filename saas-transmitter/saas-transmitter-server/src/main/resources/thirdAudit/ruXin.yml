dslType: contract
enable: true
name: 儒信处方数据上传
protocol: webservice
common: false
format: xml
domain: business.data['network']['networkItem']['url']
functions:
  - path: "'/soap/ws'"
    bodyStrategy: xmlSoap
    protocol: webservice
    timeout: 180 # 超时时间(秒)
    request:
      method: POST
      header:
        "Content-Type": "'text/xml; charset=utf-8'"
        "SOAPAction": "''"
      body:
        "namespace": "'http://webservice.rxrtoutreachweb.com'"
        "methodName": "'saveRecipelJson'"
        "methodData":
          arg0: business.data['aux']['ruXinPrescriptionInfo']
          arg1: business.data['aux']['ruXinPrescriptionDetail']
          arg2: "'Z7HzfC2BAhn6PMWj'"
    response:
      # 根据实际API响应结构调整
      "success": "T(com.alibaba.fastjson.JSON).parseObject(['saveRecipelJsonResponse']['String']).getString('code') == '0000' && T(com.alibaba.fastjson.JSON).parseObject(T(com.alibaba.fastjson.JSON).parseObject(['saveRecipelJsonResponse']['String']).getJSONArray('data').getString(0)).getString('code') == '0000'"
      "code": "T(com.alibaba.fastjson.JSON).parseObject(['saveRecipelJsonResponse']['String']).getString('code') == '0000' && T(com.alibaba.fastjson.JSON).parseObject(T(com.alibaba.fastjson.JSON).parseObject(['saveRecipelJsonResponse']['String']).getJSONArray('data').getString(0)).getString('code') == '0000' ? 0 : 1"
