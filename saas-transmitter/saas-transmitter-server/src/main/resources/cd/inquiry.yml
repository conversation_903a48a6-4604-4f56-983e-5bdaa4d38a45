dslType: contract
enable: true
name: 处方信息上送
format: json
common: false
domain: business.data['network']['networkItem']['url']
functions:
  - path: "'/wjw/upload/uploadFurtherConsult'"
    bodyStrategy: jsonNode
    request:
      method: POST
      header:
        "Content-Type": "'application/json'"
      body:
        #请求token
        "accessToken": "T(com.xyy.saas.transmitter.server.util.transmission.drug.RequestSignUtil).getAccessToken(business.data['network']['networkItem']['url']+'/wjw/third/oauth/getAccessToken','54ee872bad564f08a66c51b76b03aa53','aa9c09ac2e2d4831a665256d01ab6afe')"
        "clientId": "'54ee872bad564f08a66c51b76b03aa53'"
        "thirdUniqueid": business.data['data']['pref']
        "orgName": "'成都双流宜贰叁互联网医院'"
        "orgCode": "'MA643QRQ-3'"
        "channelName": "'荷叶健康'"
        #科室名称
        "section": "T(com.xyy.saas.transmitter.server.util.transmission.drug.CdDeptCodeChangeEnum).isInCdDeptCodeChangeEnum(business.data['data']['deptPref'])?T(com.xyy.saas.transmitter.server.util.transmission.drug.CdDeptCodeChangeEnum).getEnumByHyCode(business.data['data']['deptPref']).getDeptName():business.data['data']['deptName']"
        #科室编码
        "sectionCode": "T(com.xyy.saas.transmitter.server.util.transmission.drug.CdDeptCodeChangeEnum).isInCdDeptCodeChangeEnum(business.data['data']['deptPref'])?T(com.xyy.saas.transmitter.server.util.transmission.drug.CdDeptCodeChangeEnum).getEnumByHyCode(business.data['data']['deptPref']).getCode():T(java.lang.Integer).parseInt(T(org.apache.commons.lang3.StringUtils).right(business.data['data']['deptPref'],3))"
        #医生名称
        "docName": business.data['data']['doctorName']
        "certificateNum": business.data['aux']['doctorInfo']['professionalNo']
        # 病人姓名
        "patientName": business.data['data']['patientName'] # 患者姓名
        # 病人年龄
        "patientAge": business.data['aux']['inquiryDetailInfo']?.patientAge # 患者年龄
        # 病人性别
        "patientSex": "T(java.util.Objects).equals(business.data['data']['patientSex'], '1') ? '男' : '女'" # 患者性别转换：1(男)->0, 2(女)->1
        #证据类别 1身份证
        "patientIdcardType": 1
        #身份证号
        "patientIdcardNum": business.data['data']['idCard']
        "furtherConsultNo": business.data['data']['pref']
        #问诊方式
        "furtherConsulType": "business.data['data']['inquiryWayType'] == 1 ? 1: 3"
        #过敏史
        "medicalHistory": "T(cn.hutool.core.collection.CollUtil).isEmpty(business.data['aux']['inquiryDetailInfo']?.allergic)?'无': T(org.apache.commons.lang3.StringUtils).join(business.data['aux']['inquiryDetailInfo']?.allergic, '|')"
        #问诊时间
        "furtherConsultApplyTime": T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(business.data['aux']['inquiryInfo']?.createTime)
        "furtherConsulStartTime": T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(business.data['data']['outPrescriptionTime'])
        "furtherConsulEndTime": T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(business.data['aux']['inquiryInfo']?.endTime)
        "furtherConsulIsReply": 1
        "feeType": 1
        "furtherConsultDiagnosis": "T(org.apache.commons.lang3.StringUtils).join(business.data['data']['diagnosisName'], '|')"
        "furtherConsultDiagnosisNo": "T(org.apache.commons.lang3.StringUtils).join(business.data['data']['diagnosisCode'], '|')"
        "furtherConsultPrice": 0
        "patientEvaluate": 1
        "complainInfo": "'无'"
        "disposeResult": "'无'"
        "isRiskWarn": 1
        "isPatientSign": 0
        "isPrescription": 1
        "uploadTime": T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(T(java.time.LocalDateTime).now())
        "cityId": "'510116'"
        #当前年份
        "year": "T(java.lang.String).valueOf(T(java.time.LocalDate).now().getYear())"
        "isMark": "'1'"
        "consultDiagnosisType": 1
        "consultDiagnosis": "T(org.apache.commons.lang3.StringUtils).join(business.data['data']['diagnosisName'],'|')"
        "consultOrg": business.data['aux']['doctorInfo']?.firstPracticeName
        #首诊时间
        "consultTime": "T(com.xyy.saas.transmitter.server.util.transmission.drug.RequestSignUtil).getRandomConsultTime(T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(business.data['data']['outPrescriptionTime']))"


    response:
      # 根据实际API响应结构调整
      "success": "['success']"
      "message": "['message']"
      "code": "['status']"

