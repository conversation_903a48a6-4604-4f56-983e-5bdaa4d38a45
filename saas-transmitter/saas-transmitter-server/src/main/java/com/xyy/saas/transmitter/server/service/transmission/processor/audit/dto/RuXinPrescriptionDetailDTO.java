package com.xyy.saas.transmitter.server.service.transmission.processor.audit.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 处方药品明细表
 * </p>
 */
@Data
public class RuXinPrescriptionDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 处方ID
     */
    private Long tprSeqId;

    /**
     * 商品ID
     */
    private String spid;

    /**
     * 商品编号
     */
    private String spbh;

    /**
     * 商品条码
     */
    private String sptm;

    /**
     * 商品名称
     */
    private String spmch;

    /**
     * 助记码
     */
    private String zjm;

    /**
     * 生产厂家
     */
    private String shengccj;

    /**
     * 商品规格
     */
    private String shpgg;

    /**
     * 单位
     */
    private String dw;

    /**
     * 数量
     */
    private BigDecimal num;

    /**
     * 用法用量
     */
    private String yfyl;

    /**
     * 是否实名
     */
    private String isSmz;

    /**
     * 效期
     */
    private String xiaoqi;

    /**
     * 批准文号
     */
    private String pzwh;

    /**
     * 批号
     */
    private String piHao;

    /**
     * 机构ID
     */
    private Long deptId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;


    private Long id;

    /**
     * 预留字段1
     */
    private String preset1;

    /**
     * 预留字段2
     */
    private String preset2;

    /**
     * 预留字段3
     */
    private String preset3;

    /**
     * 预留字段4
     */
    private String preset4;

    /**
     * 是否含麻
     */
    private String isMhj;
    


}
