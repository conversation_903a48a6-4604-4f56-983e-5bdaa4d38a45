package com.xyy.saas.transmitter.server.service.transmission.processor.audit.dto;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 儒信处方记录表
 * </p>
 *
 */
@Data
public class RuXinPrescriptionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 处方ID
     */
    private Long tprSeqId;

    /**
     * 咨询记录ID
     */
    private Long consSeqId;

    /**
     * 开方日期
     */
    private Date tprCreateDate;

    /**
     * 开方时间
     */
    private Date tprCreateTime;

    /**
     * 处方来源
     */
    private String tprSoruce;

    /**
     * 患者姓名
     */
    private String tprName;

    /**
     * 年龄
     */
    private Integer tprAge;

    /**
     * 性别（数据字典）
     */
    private String tprSex;

    /**
     * 地址
     */
    private String tprAddress;

    /**
     * 联系电话
     */
    private String tprPhone;

    /**
     * 科室
     */
    private String tprDepartment;

    /**
     * 诊断
     */
    private String tprDiagnostic;

    /**
     * 图片处方路径
     */
    private String tprImgData;

    /**
     * 开方医师
     */
    private String tprDoctor;

    /**
     * 医师ID
     */
    private Long tprDoctorId;

    /**
     * 处方日期
     */
    private Date tprLrRq;

    /**
     * 处方类型（数据字典）
     */
    private Integer prescribeType;

    /**
     * 备注/审核意见
     */
    private String auitMemo;

    /**
     * 审核状态（数据字典）
     */
    private String auditState;

    /**
     * 购买人姓名
     */
    private String tprBuyName;

    /**
     * 购买人身份证号
     */
    private String tprBuyNo;

    /**
     * 审核药师ID
     */
    private Long passId;

    /**
     * 审核日期
     */
    private Date passDate;

    /**
     * 审核时间
     */
    private Date passTime;


    /**
     * 处方状态0正常1正在审核
     */
    private Integer tprStatus;

    /**
     * 操作人
     */
    private String tprOper;

    /**
     * 发药员
     */
    private String tprFy;

    /**
     * 机构ID
     */
    private Long deptId;

    /**
     * 药品类型（数据字典）
     */
    private Integer drugType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 调配人
     */
    private String tprTpr;

    /**
     * 核发人
     */
    private String tprHdr;

    /**
     * 是否销售0未销售1已销售
     */
    private Integer isXiaos;

    /**
     * 销售日期
     */
    private Date xsDate;

    /**
     * 销售时间
     */
    private Date xsTime;

    /**
     * 销售员
     */
    private String xsYuan;

    /**
     * erp销售单号
     */
    private String xsErpno;

    /**
     * 预留字段1
     */
    private String preset1;

    /**
     * 预留字段2
     */
    private String preset2;

    /**
     * 预留字段3
     */
    private String preset3;

    /**
     * 预留字段4
     */
    private String preset4;

    /**
     * 锁定状态
     */
    private String lockStatus;

    private Integer version;

    //回调路径
    private String callBackUrl;

    //ERP处方流水号
    private String erpSeqSn;
}
