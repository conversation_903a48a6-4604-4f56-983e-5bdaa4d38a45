package com.xyy.saas.transmitter.server.service.transmission.processor.audit;

import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import org.springframework.stereotype.Component;

/**
 * 三方审方默认处理器
 */
@Component
public class AuditDefaultLogicConfigProcessor extends AuditLogicConfigProcessor {

    @Override
    public NodeTypeEnum getNodeType() {
        // 返回 null，表示这是一个通用的机构类型处理器
        // 在 LogicConfigProcessorFactory.initProcessors 中会被注册到 organTypeProcessorMap
        return null;
    }
}
