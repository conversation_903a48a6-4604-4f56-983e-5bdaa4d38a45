package com.xyy.saas.transmitter.server.service.transmission.processor.audit;

import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionTransmitterDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.PostParameterConfig;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.PreParameterConfig;
import com.xyy.saas.transmitter.server.service.transmission.processor.drug.DrugLogicConfigProcessor;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 互联网监管-在线处方明细信息上报处理器
 * <p>
 * 处理在线处方审核通过后的信息上报至陕西省互联网医院监管平台
 * <p>
 * 处理职责： 1. 参数校验：验证处方相关参数 2. 数据处理：处理处方信息、审方信息、药品信息 3. 结果转换：转换为监管平台要求的格式
 */
@Slf4j
@Component
@AllArgsConstructor
public class AuditPrescriptionDetailUploadProcessor extends DrugLogicConfigProcessor {

    @Override
    public NodeTypeEnum getNodeType() {
        return NodeTypeEnum.AUDIT_PRESCRIPTION_DETAIL_UPLOAD;
    }

    @Override
    protected void serializeObj(TransmissionReqDTO transmissionReqDTO) {
        final PrescriptionTransmitterDTO dataObj = transmissionReqDTO.getDataObj(PrescriptionTransmitterDTO.class);
        transmissionReqDTO.setData(dataObj)
            .setFullName(dataObj.getFullName())
            .setIdCard(dataObj.getIdCard())
            .setBusinessNo(dataObj.getBusinessNo());
    }

    /**
     * 前置参数填充 在业务处理前填充必要的实时数据（如快照数据）
     * <p>
     * 处理内容： 1. 填充实时数据 2. 组装快照信息 3. 转换请求参数
     *
     * @param transmissionReqDTO 传输请求对象
     * @param config             前置参数配置
     */
    @Override
    protected void fillPreProcessParameters(TransmissionReqDTO transmissionReqDTO,
        PreParameterConfig config) {
        super.fillPreProcessParameters(transmissionReqDTO, config);

        PrescriptionTransmitterDTO data = (PrescriptionTransmitterDTO) transmissionReqDTO.getData();
        // 填充处方详情信息
        transmissionPrescriptionService.fillPrescriptionDetailInfo(transmissionReqDTO.getAux(), config.getNodes(), data.getPref());
        // 填充门店信息
        transmissionPrescriptionService.fillTenantInfo(transmissionReqDTO.getAux(), config.getNodes(), transmissionReqDTO.getConfig().getTenantId());
    }

    @Override
    protected void fillPostProcessParameters(TransmissionReqDTO transmissionReqDTO, PostParameterConfig config) {
        super.fillPostProcessParameters(transmissionReqDTO, config);
        // 添加在线处方特有的后置参数处理逻辑
        PrescriptionTransmitterDTO data = (PrescriptionTransmitterDTO) transmissionReqDTO.getData();

        // 填充处方相关数据
        transmissionPrescriptionService.fillPrescriptionParameters(transmissionReqDTO, config, data);
    }


}
