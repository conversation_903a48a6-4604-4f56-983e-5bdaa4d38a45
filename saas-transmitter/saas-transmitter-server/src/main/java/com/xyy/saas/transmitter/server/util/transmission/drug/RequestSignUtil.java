package com.xyy.saas.transmitter.server.util.transmission.drug;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.KeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/24 19:46
 */
@Slf4j
public class RequestSignUtil {

    public static final String AUTHORIZATION_FORMAT_V21 = "SUPERVISION-INST-V3-SHA256-RSA1024 %s,%s,%s";

    public static final String SHA_256_RSA = "SHA256withRSA";

    public static void main(String[] args) {

        String body = "{\"prcSocialCreditCode\":\"91530100MA6QFMR80Y\",\"prcEnterpriseName\":\"云南智鹿大药房连锁有限公司\",\"seqNo\":\"2025072813392991530100MA6QFMR80YHYWZ104232\",\"prescriptionNo\":\"HYWZ104232\",\"hospitalName\":\"陈笑一-测试格林医院\",\"doctorName\":\"陈医\",\"prescribeDate\":\"20250728\",\"drugstoreSocialCreditCode\":\"91530328MA6PBJBD9x\",\"drugstoreNo\":\"滇DA2928021\",\"drugstoreName\":\"永平县福康大药房\",\"drugstoreAreaNo\":\"88801\",\"patientName\":\"陈测\",\"patientGender\":\"1\",\"patientAge\":\"28\",\"patientPhone\":\"15926350018\",\"diagnosis\":\"急性上呼吸道感染\",\"imgUrl\":\"https://files.test.ybm100.com/INVT/Lzinq/20250728/c7cfd075a558ab99c648d49a07348c25c3b7b03eb976c9130671db27a238793d.png\",\"registrationNo\":\"531222010194\",\"verificationTime\":\"20250728134317\",\"drugList\":[{\"drugNo\":\"国药准字*********\",\"drugName\":\"感冒灵片\",\"spec\":\"60s\",\"amount\":\"1.000\",\"unit\":\"盒\",\"instructions\":\"口服  一次 4 片 3次/天\"},{\"drugNo\":\"国药准字*********\",\"drugName\":\"头孢克洛片\",\"spec\":\"0.25G*10片\",\"amount\":\"1.000\",\"unit\":\"盒\",\"instructions\":\"口服  一次 1 片 3次/天\"}]}";

        String s = sign4YunNanDrugSupervision("91530100MA6QFMR80Y", "/openapi/middleman/prc/prescription", body,
            "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAMHB5BKCPxMumoXa26d4l7/rA4aQ8cPdMb11a+waJQTH1X3cNVEHYcde57oYlKkhqU37tJim98HXFQK6rFXWqmGgvS+iXpE9X0KmnzDk8Kk10H7vA3VZDxWBcxzcgPG198YRQoeM24jbgJ7hDbyeuh6eegOcO7pfdmuLT3hx+OW7AgMBAAECgYBRdBBMU6LujnX5daNHySOKU+McjAyG+ZMygY2IKQmM8/FlQtDLKp5JcTVbf3/nhjYliX5HelHHlILJre5K+76OJtq0Yk5R093u5BlyGOZSKOuLxaN7+WZA4izGRWGIBT6k8E2m+t6lmk6mIq9LiHSGNnOTCUGXA/nfqYyFwG9RWQJBAOfuvqsplQ/exgOPsU/30tF31tP0czX1+0OjnMI5BOJ1TapWW6mMNqBWHAsXJybZfPGwdtlJ7b6RgOeMn6Si0/0CQQDV3Qb7jIzgzMG2W0pMmAZo+fT3W19YRfOhOjHV/NV382bgygCtCiEwabzxqIL607zOsf2OlD8SMNy+k2GbRWIXAkEAx+T46ER6eKckOsWJqMaLKIYE3cD92zAaUv2cibjjANrhCUJM1iBGpIISwWw/Dji8ewIALN6OzYRSeT62CQtdcQJASKz+1Xby3OXYIl8thY/YBc6Dd5eedAH5g92h1+U+qa+WU2dyCVu64ZfdNIYeVJyludv0nYI1WLowGbc94SUX6wJADeOEnhiXcT/lhjyW+xEVjUZYF7Tq7Xrr5oQqE4Z90CEqsfIGLoFAXZTsle9nc2Nd2edKJvfMndOQ8AennFdi8w==");
        System.out.println(s);
    }

    /**
     * 云南药品药监Sign签名
     *
     * @param socialCreditCode
     * @param absoluteUrlPath
     * @param body
     * @param privateKey
     * @return
     */
    public static String sign4YunNanDrugSupervision(String socialCreditCode, String absoluteUrlPath, String body, String privateKey) {
        long timestamp = System.currentTimeMillis();
        String method = "POST";
        try {
            //构建签名串  method + absoluteUrlPath + timestamp + body   每一行为一个参数,行尾以 \n（换行符，ASCII 编码值为 0x0A）结束，最后一行无需换行符。
            String message = method + "\n"
                + absoluteUrlPath + "\n"
                + timestamp + "\n"
                + body;
            Signature signature = Signature.getInstance(SHA_256_RSA);
            signature.initSign(loadPrivateKey(privateKey));
            signature.update(message.getBytes(StandardCharsets.UTF_8));
            String sign = Base64.getEncoder().encodeToString(signature.sign());
            //构建Authorization
            return String.format(AUTHORIZATION_FORMAT_V21, socialCreditCode, timestamp, sign);
        } catch (Exception e) {
            log.error("sign4YunNanDrugSupervision error,socialCreditCode:{},timestamp:{},absoluteUrlPath:{},body:{}", socialCreditCode, timestamp, absoluteUrlPath, body, e);
            throw new RuntimeException("云南药品药监Sign签名异常,请检查", e);
        }

    }





    public static Map<String,Object> guiZhouGenerateSignSource(String accessKey,String version,Map<String,Object> sourceParam,String key){
      try {
          if(CollUtil.isEmpty(sourceParam)){
              return new HashMap<>();
          }
          Map prescriptions = (Map) sourceParam.get("prescriptions");
          sourceParam.put("prescriptions",List.of(prescriptions));

          Map<String, Object> params = new HashMap<>();
          params.put("access_key",accessKey);
          params.put("biz_content", JsonUtils.toJsonString(sourceParam));
          params.put("format", "json");
          params.put("request_id", UUID.randomUUID().toString().replaceAll("-", "").substring(0, 20));
          params.put("timestamp",String.valueOf(System.currentTimeMillis()));
          params.put("version", version);
          String signSource = generateSignSource(params);
          String sign = sign(signSource, key);
          params.put("sign", sign);
          return params;
      }catch (Exception e){
          log.error("guiZhouGenerateSignSource error", e);
          throw new RuntimeException("贵州药监Sign签名异常,请检查", e);
      }
    }

    public static String sign(String source, String key) throws Exception {
        String sign = null;
        try {
            PrivateKey privateKey = null;
            byte[] encodedKey = Base64.getDecoder().decode(key.getBytes(StandardCharsets.UTF_8));
            KeySpec keySpec = new PKCS8EncodedKeySpec(encodedKey);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            privateKey = keyFactory.generatePrivate(keySpec);
            if (privateKey != null) {
                //签名
                Signature signature = Signature.getInstance("SHA256withRSA");
                signature.initSign(privateKey);
                signature.update(source.getBytes());
                byte[] signed = signature.sign();
                //取base64， 得到签名串
                sign = Base64.getEncoder().encodeToString(signed);
            }
        }catch (Exception e){
           log.info("sign error,source:{},key:{}", source, key);
            throw new RuntimeException("贵州药监Sign签名异常,请检查", e);
        }
        return sign;
    }



    public static String generateSignSource(Map params) {
        Set<String> keySet = params.keySet();
        List<String> keys = new ArrayList<>();
        for (String key : keySet) {
            if (params.get(key) != null && StringUtils.isNotBlank(params.get(key).toString())) {
                keys.add(key);
            }
        }
        Collections.sort(keys);
        StringBuilder builder = new StringBuilder();
        for (int i = 0, size = keys.size(); i < size; i++) {
            String key = keys.get(i);
            Object value = params.get(key);
            builder.append(key);
            builder.append("=");
            builder.append(value);
            if (i != size - 1) {
                builder.append("&");
            }
        }
        return builder.toString();
    }

    /**
     * 加载私钥
     *
     * @param privateKeyStr 私钥文件名
     * @return 是否成功
     * @throws Exception
     */
    public static RSAPrivateKey loadPrivateKey(String privateKeyStr) throws Exception {
        try {
            byte[] buffer = Base64.getDecoder().decode(privateKeyStr);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(buffer);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return (RSAPrivateKey) keyFactory.generatePrivate(keySpec);
        } catch (NoSuchAlgorithmException e) {
            throw new Exception("无此算法");
        } catch (InvalidKeySpecException e) {
            throw new Exception("私钥非法");
        } catch (NullPointerException e) {
            throw new Exception("私钥数据为空");
        }
    }




}
