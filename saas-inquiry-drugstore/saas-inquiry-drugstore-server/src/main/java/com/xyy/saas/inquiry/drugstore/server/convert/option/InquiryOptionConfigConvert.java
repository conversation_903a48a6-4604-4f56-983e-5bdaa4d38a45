package com.xyy.saas.inquiry.drugstore.server.convert.option;

import static com.xyy.saas.inquiry.drugstore.enums.InquiryTargetTypeEnum.AREA;
import static com.xyy.saas.inquiry.drugstore.enums.InquiryTargetTypeEnum.GLOBAL;
import static com.xyy.saas.inquiry.drugstore.enums.InquiryTargetTypeEnum.STORE;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.ip.core.Area;
import cn.iocoder.yudao.framework.ip.core.utils.AreaUtils;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionAreaConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionStoreConfigRespDto;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionAreaConfigReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionGlobalConfigReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionStoreConfigReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.option.InquiryOptionConfigDO;
import com.xyy.saas.inquiry.pojo.TenantDto;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Author:chenxiaoyi
 * @Date:2024/09/30 10:53
 */
@Mapper
public interface InquiryOptionConfigConvert {

    InquiryOptionConfigConvert INSTANCE = Mappers.getMapper(InquiryOptionConfigConvert.class);
    Logger log = LoggerFactory.getLogger(InquiryOptionConfigConvert.class);

    /**
     * 全局配置转DO列表
     *
     * @param global
     * @param optionList
     * @return
     */
    default List<InquiryOptionConfigDO> globalOptions2DOList(InquiryOptionGlobalConfigReqVO global, List<InquiryOptionConfigDO> optionList) {
        if (global == null) {
            return List.of();
        }
        Map<Integer, InquiryOptionConfigDO> inquiryOptionConfigMap = optionList.stream().collect(Collectors.toMap(InquiryOptionConfigDO::getOptionType, v -> v));
        // 枚举对应的配置类型
        return Arrays.stream(InquiryOptionTypeEnum.values())
            .filter(i -> GLOBAL.hasFlags(i.getTargetTypePriorities()))
            .map(hsEnum -> {
                InquiryOptionConfigDO option = Optional.ofNullable(inquiryOptionConfigMap.get(hsEnum.getType())).orElseGet(InquiryOptionConfigDO::new);
                option.setDeleted(false);
                option.setTargetType(GLOBAL.type)
                    .setTargetId(TenantConstant.DEFAULT_TENANT_ID)
                    .setTargetName("")
                    .setProvince("")
                    .setProvinceCode("")
                    .setCity("")
                    .setCityCode("")
                    .setArea("")
                    .setAreaCode("")
                    .setUsed(CommonStatusEnum.ENABLE.getStatus())
                    .setOptionType(hsEnum.getType())
                    .setOptionName(hsEnum.getField())
                    .setDescription(hsEnum.getDescription());
                return switch (hsEnum) {
                    case PROC_INQUIRY_COMPLIANCE -> option.setterOptionValue(global::getProcInquiryCompliance, false)
                        .setterExt(global::procInquiryComplianceExt, false);
                    case PROC_TELETEXT_INTERACTION_DIALOG_POPUP_SPEED -> option.setterOptionValue(global::getProcTeletextInteractionDialogPopupSpeed, false);
                    case PROC_INQUIRY_SERVICE_SWITCH -> option.setterOptionValue(global::getProcInquiryServiceSwitch, false);
                    case PROC_REPEAT_DRUG_SWITCH -> option.setterOptionValue(global::getProcRepeatDrugSwitch, false);
                    case PROC_DOCTOR_ADMISSION_DEFAULT_PAGE -> option.setterOptionValue(global::getProcDoctorAdmissionDefaultPage, false);
                    case PRES_GLOBAL_DATE_FORMAT -> option.setterOptionValue(global::getPresGlobalDateFormat, false);
                    case PRES_TELETEXT_INQUIRY_DOCTOR_ORDER_MAX -> option.setterOptionValue(global::getPresTeletextInquiryDoctorOrderMax, false);
                    case PRES_DOCTOR_2ND_CONFIRM_DIALOG -> option.setterOptionValue(global::getPresDoctor2ndConfirmDialog, false);
                    case PRES_SIGNATURE_USE_INTERVAL -> option.setterOptionValue(global::getPresSignatureUseInterval, false);
                    case PRES_DOCTOR_CANNOT_INQUIRY_AREA -> option.setterOptionValue(global::getPresDoctorCannotInquiryArea, false);
                    case PRES_AUTO_INQUIRY_TO_REAL_FOR_ALLERGIC -> option.setterOptionValue(global::getPresAutoInquiryToRealForAllergic, false);
                    case PRES_AUTO_INQUIRY_TO_REAL_FOR_LIVER_RENAL_DYSFUNCTION -> option.setterOptionValue(global::getPresAutoInquiryToRealForLiverRenalDysfunction, false);
                    case PRES_AUTO_INQUIRY_TO_REAL_FOR_PREGNANCY_LACTATION -> option.setterOptionValue(global::getPresAutoInquiryToRealForPregnancyLactation, false);
                    case PRES_AUTO_INQUIRY_TO_REAL_FOR_MULTI_DIAGNOSE -> option.setterOptionValue(global::getPresAutoInquiryToRealForMultiDiagnose, false)
                        .setterExt(global::presAutoInquiryToRealForMultiDiagnoseExt, false);
                    case PRES_AUTO_INQUIRY_TO_REAL_FOR_SPECIAL_AGE_RANGE -> option.setterOptionValue(global::getPresAutoInquiryToRealForSpecialAgeRange, false)
                        .setterExt(global::presAutoInquiryToRealForSpecialAgeRangeExt, false);
                    case PRES_AUTO_INQUIRY_RETURN_TO_REAL -> option.setterOptionValue(global::getPresAutoInquiryReturnToReal, false)
                        .setterExt(global::presAutoInquiryReturnToRealExt, false);
                    default -> option;
                };
            }).filter(Objects::nonNull).toList();
    }

    /**
     * DO列表转全局配置
     *
     * @param optionConfigList
     * @return
     */
    default InquiryOptionGlobalConfigRespDto convertDO2GlobalOptions(List<InquiryOptionConfigDO> optionConfigList) {

        InquiryOptionConfigRespDto genericOptions = convertDO2GenericOptions(optionConfigList);

        return BeanUtil.copyProperties(genericOptions, InquiryOptionGlobalConfigRespDto.class);
    }


    /**
     * 区域配置转DO列表
     *
     * @param area
     * @param optionList
     * @return
     */
    default List<InquiryOptionConfigDO> areaOptions2DOList(InquiryOptionAreaConfigReqVO area, List<InquiryOptionConfigDO> optionList) {
        if (CollUtil.isEmpty(area.getAreas())) {
            return List.of();
        }

        // 地区编码查询
        List<Area> areaList = area.getAreas().stream().map(a -> AreaUtils.getArea(a.intValue())).toList();
        // List<Area> areaList = AreaExtUtils.getDistrictAreaList(area.getArea());
        if (CollectionUtils.isEmpty(areaList)) {
            return List.of();
        }
        // 考虑多区域分组
        Map<Integer, Map<Long, InquiryOptionConfigDO>> inquiryOptionAreaConfigMap = optionList.stream().collect(
            Collectors.groupingBy(InquiryOptionConfigDO::getOptionType,
                Collectors.toMap(InquiryOptionConfigDO::getTargetId, v -> v)));
        // 枚举对应的配置类型
        return Arrays.stream(InquiryOptionTypeEnum.values())
            .filter(i -> AREA.hasFlags(i.getTargetTypePriorities()) && Objects.equals(i.getType(), area.getOptionType()))
            .flatMap(hsEnum -> {
                Map<Long, InquiryOptionConfigDO> optionAreaMap = Optional.ofNullable(inquiryOptionAreaConfigMap.get(hsEnum.getType())).orElseGet(Map::of);
                // 遍历每个地区
                return areaList.stream().map(a -> {
                    Long targetId = a.getId().longValue();
                    InquiryOptionConfigDO option = Optional.ofNullable(optionAreaMap.get(targetId)).orElseGet(InquiryOptionConfigDO::new);
                    option.setDeleted(false);
                    option.setTargetType(AREA.type)
                        .setTargetId(targetId)
                        .setTargetName(a.getName())
                        .setProvince(a.getParent().getParent().getName())
                        .setProvinceCode("" + a.getParent().getParent().getId())
                        .setCity(a.getParent().getName())
                        .setCityCode("" + a.getParent().getId())
                        .setArea(a.getName())
                        .setAreaCode("" + a.getId())
                        .setOptionType(hsEnum.getType())
                        .setOptionName(hsEnum.getField())
                        .setUsed(CommonStatusEnum.ENABLE.getStatus())
                        .setDescription(hsEnum.getDescription());
                    return switch (hsEnum) {
                        case FORM_ANTIMICROBIAL_DRUG -> option.setterOptionValue(area::getFormAntimicrobialDrugCatalogId, false);
                        // case PROC_PRESCRIPTION_TYPE_INPUT -> option.setterOptionValue(area::getProcPrescriptionTypeRequired, false);
                        // case PROC_HOME_ADDRESS_INPUT -> option.setterOptionValue(area::getProcHomeAddressRequired, false);
                        case PROC_GUARDIAN_INPUT -> option.setterOptionValue(area::getOptionValue, false).setterExt(area::procGuardianAgeExt, false);
                        case PROC_INQUIRY_COMPLIANCE -> option.setterOptionValue(area::getProcInquiryCompliance, false)
                            .setterExt(area::procInquiryComplianceExt, false);
                        case PROC_VIDEO_INQUIRY -> option.setterOptionValue(area::getProcVideoInquiry, false)
                            .setterExt(area::procVideoInquiryExt, false);
                        case PROC_DOCTOR_ADMISSION_DEFAULT_PAGE -> option.setterOptionValue(area::getProcDoctorAdmissionDefaultPage, false);
                        case PRES_ALL_REAL_PEOPLE_INQUIRY -> option.setterExt(area::presAllRealPeopleInquiryExt, false);
                        case PROC_IDENTITY_REQUIRED -> option.setterExt(area::areaInquiryHospitalPrefExt, false);
                        case PROC_INQUIRY_PRODUCT_DOCTOR -> option.setterExt(area::areaInquiryProductDoctorExt, false);
                        // case PRES_PHARMACIST_AREA_TYPE -> option.setterOptionValue(area::getOptionValue, false);
                        default -> option.setOptionValue(StringUtils.defaultIfBlank(area.getOptionValue(), Boolean.TRUE.toString()));
                    };
                });
            }).filter(Objects::nonNull).toList();
    }


    /**
     * DO转区域配置
     *
     * @param option
     * @return
     */
    default InquiryOptionAreaConfigRespDto convertDO2AreaOptions(InquiryOptionConfigDO option) {
        InquiryOptionAreaConfigRespDto dto = BeanUtils.toBean(option, InquiryOptionAreaConfigRespDto.class);

        InquiryOptionConfigRespDto genericOptions = convertDO2GenericOptions(List.of(option));

        // null属性忽略
        BeanUtil.copyProperties(genericOptions, dto, CopyOptions.create().ignoreNullValue());
        return dto;
    }


    /**
     * 门店配置转DO列表
     *
     * @param store
     * @param tenantDto
     * @param optionList
     * @return
     */
    default List<InquiryOptionConfigDO> storeOptions2DOList(InquiryOptionStoreConfigReqVO store, TenantDto tenantDto, List<InquiryOptionConfigDO> optionList) {
        if (store == null || tenantDto == null) {
            return List.of();
        }
        Map<Integer, InquiryOptionConfigDO> inquiryOptionConfigMap = optionList.stream().collect(Collectors.toMap(InquiryOptionConfigDO::getOptionType, v -> v));
        // 枚举对应的配置类型
        return Arrays.stream(InquiryOptionTypeEnum.values())
            .filter(i -> STORE.hasFlags(i.getTargetTypePriorities()) && Objects.equals(i.getType(), store.getOptionType()))
            .map(hsEnum -> {
                InquiryOptionConfigDO option = Optional.ofNullable(inquiryOptionConfigMap.get(hsEnum.getType())).orElseGet(InquiryOptionConfigDO::new);
                option.setDeleted(false);
                option.setTargetType(STORE.type)
                    .setTargetId(tenantDto.getId())
                    .setTargetName(tenantDto.getName())
                    .setProvince("")
                    .setProvinceCode("")
                    .setCity("")
                    .setCityCode("")
                    .setArea("")
                    .setAreaCode("")
                    .setOptionType(hsEnum.getType())
                    .setOptionName(hsEnum.getField())
                    .setUsed(store.getUsed() == null ? CommonStatusEnum.ENABLE.getStatus() : store.getUsed())
                    .setDescription(hsEnum.getDescription());
                return switch (hsEnum) {
                    case FORM_ANTIMICROBIAL_DRUG -> option.setterOptionValue(store::getFormAntimicrobialDrugCatalogId, false);
                    // case PROC_PRESCRIPTION_TYPE_INPUT -> option.setterOptionValue(store::getProcPrescriptionTypeRequired, false);
                    // case PROC_HOME_ADDRESS_INPUT -> option.setterOptionValue(store::getProcHomeAddressRequired, false);
                    case PROC_GUARDIAN_INPUT -> option.setterOptionValue(store::getOptionValue, false).setterExt(store::procGuardianAgeExt, false);
                    case PROC_VIDEO_INQUIRY -> option.setterOptionValue(store::getProcVideoInquiry, false)
                        .setterExt(store::procVideoInquiryExt, false);
                    case PROC_DOCTOR_ADMISSION_DEFAULT_PAGE -> option.setterOptionValue(store::getProcDoctorAdmissionDefaultPage, false);
                    default -> option.setOptionValue(StringUtils.defaultIfBlank(store.getOptionValue(), Boolean.TRUE.toString()));
                };
            }).filter(Objects::nonNull).toList();
    }


    /**
     * DO转门店配置
     *
     * @param option
     * @return
     */
    default InquiryOptionStoreConfigRespDto convertDO2StoreOptions(InquiryOptionConfigDO option) {
        InquiryOptionStoreConfigRespDto dto = BeanUtils.toBean(option, InquiryOptionStoreConfigRespDto.class)
            .setTenantId(option.getTargetId())
            .setTenantName(option.getTargetName());

        InquiryOptionConfigRespDto genericOptions = convertDO2GenericOptions(List.of(option));

        // null属性忽略
        BeanUtil.copyProperties(genericOptions, dto, CopyOptions.create().ignoreNullValue());
        return dto;
    }


    /**
     * DO转通用（全局 & 区域 & 门店）配置
     *
     * @param optionList
     * @return
     */
    default InquiryOptionConfigRespDto convertDO2GenericOptions(List<InquiryOptionConfigDO> optionList) {
        InquiryOptionConfigRespDto dto = new InquiryOptionConfigRespDto();

        for (InquiryOptionConfigDO option : optionList) {
            InquiryOptionTypeEnum optionTypeEnum = InquiryOptionTypeEnum.fromType(option.getOptionType());
            if (optionTypeEnum == null) {
                continue;
            }

            // 反射赋值
            Field field = InquiryOptionConfigRespDto.getField(optionTypeEnum.getField());
            if (field == null) {
                continue;
            }
            try {
                // 行记录存在,就是配置生效,除非是禁用
                if (field.getType().isAssignableFrom(Boolean.class)) {
                    field.set(dto, !CommonStatusEnum.isDisable(option.getUsed()));
                }
                if (field.getType().isAssignableFrom(Integer.class)) {
                    field.set(dto, NumberUtil.parseInt(option.getOptionValue(), null));
                }
                if (field.getType().isAssignableFrom(Long.class)) {
                    field.set(dto, NumberUtil.parseLong(option.getOptionValue(), null));
                }
                if (field.getType().isAssignableFrom(String.class)) {
                    field.set(dto, option.getOptionValue());
                }
            } catch (IllegalAccessException e) {
                log.error("Field【{}.{}】setter skipped with error ：{}", InquiryOptionConfigRespDto.class.getSimpleName(), field.getName(), e.getMessage(), e);
            }

            // 补充字段赋值
            switch (optionTypeEnum) {
                // case PROC_TELETEXT_INTERACTION_DIALOG_POPUP_SPEED -> dto.setProcTeletextInteractionDialogPopupSpeed(NumberUtil.parseInt(option.getOptionValue(), null));
                // case PROC_INQUIRY_SERVICE_SWITCH -> dto.setProcInquiryServiceSwitch(ObjUtil.defaultIfNull(BooleanUtil.toBooleanObject(option.getOptionValue()), true));
                // case PRES_GLOBAL_DATE_FORMAT -> dto.setPresGlobalDateFormat(option.getOptionValue());
                // case PRES_TELETEXT_INQUIRY_DOCTOR_ORDER_MAX -> dto.setPresTeletextInquiryDoctorOrderMax(NumberUtil.parseInt(option.getOptionValue(), null));
                // case PRES_DOCTOR_2ND_CONFIRM_DIALOG -> dto.setPresDoctor2ndConfirmDialog(BooleanUtil.toBooleanObject(option.getOptionValue()));
                // case PRES_SIGNATURE_USE_INTERVAL -> dto.setPresSignatureUseInterval(NumberUtil.parseInt(option.getOptionValue(), null));
                // case PRES_DOCTOR_CANNOT_INQUIRY_AREA -> dto.setPresDoctorCannotInquiryArea(option.getOptionValue());
                // case PRES_AUTO_INQUIRY_TO_REAL_FOR_ALLERGIC -> dto.setPresAutoInquiryToRealForAllergic(BooleanUtil.toBooleanObject(option.getOptionValue()));
                // case PRES_AUTO_INQUIRY_TO_REAL_FOR_LIVER_RENAL_DYSFUNCTION -> dto.setPresAutoInquiryToRealForLiverRenalDysfunction(BooleanUtil.toBooleanObject(option.getOptionValue()));
                // case PRES_AUTO_INQUIRY_TO_REAL_FOR_PREGNANCY_LACTATION -> dto.setPresAutoInquiryToRealForPregnancyLactation(BooleanUtil.toBooleanObject(option.getOptionValue()));
                // case PRES_AUTO_INQUIRY_TO_REAL_FOR_MULTI_DIAGNOSE -> dto.setPresAutoInquiryToRealForMultiDiagnose(BooleanUtil.toBooleanObject(option.getOptionValue()))
                //     .extToThis(option.getExt());
                // case PRES_AUTO_INQUIRY_TO_REAL_FOR_SPECIAL_AGE_RANGE -> dto.setPresAutoInquiryToRealForSpecialAgeRange(BooleanUtil.toBooleanObject(option.getOptionValue()))
                //     .extToThis(option.getExt());
                // case PRES_AUTO_INQUIRY_RETURN_TO_REAL -> dto.setPresAutoInquiryReturnToReal(BooleanUtil.toBooleanObject(option.getOptionValue()))
                //     .extToThis(option.getExt());
                case FORM_ANTIMICROBIAL_DRUG -> dto.setFormAntimicrobialDrugCatalogId(NumberUtil.parseLong(option.getOptionValue(), null));
                case PROC_PRESCRIPTION_TYPE_INPUT -> dto.setProcPrescriptionTypeRequired(BooleanUtil.toBooleanObject(option.getOptionValue()));
                case PROC_HOME_ADDRESS_INPUT -> dto.setProcHomeAddressRequired(BooleanUtil.toBooleanObject(option.getOptionValue()));
                case PROC_GUARDIAN_INPUT -> dto.setProcGuardianRequired(BooleanUtil.toBooleanObject(option.getOptionValue())).extToThis(option.getExt());
                // case PROC_INQUIRY_COMPLIANCE -> dto.setProcInquiryCompliance(BooleanUtil.toBooleanObject(option.getOptionValue()))
                //     .extToThis(option.getExt());
                // case PROC_VIDEO_INQUIRY -> dto.setProcVideoInquiry(BooleanUtil.toBooleanObject(option.getOptionValue()))
                //     .extToThis(option.getExt());
                // case PROC_DOCTOR_ADMISSION_DEFAULT_PAGE -> dto.setProcDoctorAdmissionDefaultPage(option.getOptionValue());

                case FORM_WESTERN_MEDICINE_DIAGNOSIS -> dto.setFormWesternMedicineDiagnosisRequired(BooleanUtil.toBooleanObject(option.getOptionValue()));
                case FORM_TCM_SYNDROME_TREATMENT_METHODS -> dto.setFormTcmSyndromeTreatmentMethodsRequired(BooleanUtil.toBooleanObject(option.getOptionValue()));

                case FORM_TCM_SYNDROME -> dto.setFormTcmSyndromeRequired(BooleanUtil.toBooleanObject(option.getOptionValue()));
                case FORM_TCM_TREATMENT_METHODS -> dto.setFormTcmTreatmentMethodsRequired(BooleanUtil.toBooleanObject(option.getOptionValue()));

                case PROC_UPLOAD_FOLLOWUP_CERTIFICATE -> {
                    dto.setProcUploadFollowupCertificate(true);
                    dto.setProcUploadFollowupCertificateRequired(CommonStatusEnum.isEnable(option.getUsed()) && BooleanUtil.toBooleanObject(option.getOptionValue()));
                }
                default -> dto.extToThis(option.getExt());


            }
        }
        return dto;
    }


    List<InquiryOptionAreaConfigRespDto> convertDtos(List<InquiryOptionConfigDO> doList);

    InquiryOptionConfigDto convertDo2Dto(InquiryOptionConfigDO inquiryOptionConfig);
}
