package com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo;

import com.xyy.saas.inquiry.pojo.condition.ConditionGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Schema(description = "管理后台 - 全局-问诊配置选项新增/修改 Request VO")
@Data
public class InquiryOptionGlobalConfigReqVO implements Serializable {

    // 问诊流程配置-问诊合规配置(全局)
    @Schema(description = "问诊合规配置", example = "true")
    private Boolean procInquiryCompliance;

    @Schema(description = "问诊合规配置-患者年龄限制-大于等于", example = "15")
    @Max(value = 150, message = "请输入年龄区间0-150")
    @Min(value = 0, message = "请输入年龄区间0-150")
    private Integer procInquiryComplianceForPatientAgeGe;

    @Schema(description = "问诊合规配置-患者年龄限制-且小于", example = "60")
    @Max(value = 150, message = "请输入年龄区间0-150")
    @Min(value = 0, message = "请输入年龄区间0-150")
    private Integer procInquiryComplianceForPatientAgeLt;

    @Schema(description = "问诊合规配置-妊娠哺乳是否可发起问诊", example = "true")
    private Boolean procInquiryComplianceAllowForPregnancyLactation;

    // 问诊合规配置扩展
    public Map<String, Object> procInquiryComplianceExt() {
        return procInquiryCompliance == null ? null : new HashMap<>() {{
            put("procInquiryComplianceForPatientAgeGe", procInquiryComplianceForPatientAgeGe);
            put("procInquiryComplianceForPatientAgeLt", procInquiryComplianceForPatientAgeLt);
            put("procInquiryComplianceAllowForPregnancyLactation", procInquiryComplianceAllowForPregnancyLactation);
        }};
    }

    // 问诊流程配置-图文交互对话弹出速度
    @Schema(description = "图文交互对话弹出速度", requiredMode = Schema.RequiredMode.REQUIRED, example = "500")
    // @NotNull(message = "图文交互对话弹出速度不能为空")
    @Min(value = 0, message = "图文交互对话弹出速度不能小于0")
    private Integer procTeletextInteractionDialogPopupSpeed;

    // 问诊流程配置-问诊服务开关
    @Schema(description = "问诊服务开关", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    // @NotNull(message = "问诊服务开关不能为空")
    private Boolean procInquiryServiceSwitch;


    // 问诊流程配置-重复用药全局开关
    @Schema(description = "重复用药全局开关", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean procRepeatDrugSwitch;

    // 问诊流程配置-医生接诊默认页面
    @Schema(description = "医生接诊默认页面", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    // @NotNull(message = "医生接诊默认页面不能为空")
    private String procDoctorAdmissionDefaultPage;


    // 医生开方配置-开方基础属性配置
    @Schema(description = "全局处方日期格式", requiredMode = Schema.RequiredMode.REQUIRED, example = "yyyyMMddHHmmss")
    // @NotEmpty(message = "全局处方日期格式不能为空")
    private String presGlobalDateFormat;

    @Schema(description = "图文问诊医生可同时最大接单数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "5")
    // @NotNull(message = "图文问诊医生可同时最大接单数量不能为空")
    @Min(value = 0, message = "图文问诊医生可同时最大接单数量不能小于0")
    private Integer presTeletextInquiryDoctorOrderMax;

    @Schema(description = "医生开方二次确认弹窗", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    // @NotNull(message = "医生开方二次确认弹窗为空")
    private Boolean presDoctor2ndConfirmDialog;

    @Schema(description = "处方签名使用时间间隔", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @Min(value = 0, message = "处方签名使用时间间隔不能小于0")
    private Integer presSignatureUseInterval;

    @Schema(description = "医生不可接诊区域", requiredMode = Schema.RequiredMode.REQUIRED, example = "420100")
    // @NotEmpty(message = "医生不可接诊区域为空")
    private String presDoctorCannotInquiryArea;

    @Schema(description = "过敏史是否流向真人问诊", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    // @NotNull(message = "过敏史是否流向真人问诊不能为空")
    private Boolean presAutoInquiryToRealForAllergic;

    @Schema(description = "肝、肾功能异常是否流向真人问诊", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    // @NotNull(message = "肝、肾功能异常是否流向真人问诊不能为空")
    private Boolean presAutoInquiryToRealForLiverRenalDysfunction;

    @Schema(description = "妊娠、哺乳期是否流向真人问诊", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    // @NotNull(message = "妊娠、哺乳期是否流向真人问诊不能为空")
    private Boolean presAutoInquiryToRealForPregnancyLactation;


    @Schema(description = "多诊断是否流向真人问诊", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    // @NotNull(message = "多诊断是否流向真人问诊不能为空")
    private Boolean presAutoInquiryToRealForMultiDiagnose;

    @Schema(description = "多诊断是否流向真人问诊-流入比例", requiredMode = Schema.RequiredMode.REQUIRED, example = "50")
    @Max(value = 100, message = "多诊断是否流向真人问诊-流入比例不能超过100")
    @Min(value = 0, message = "多诊断是否流向真人问诊-流入比例不能小于0")
    private Integer presAutoInquiryToRealForMultiDiagnoseRatio;

    @Schema(description = "多诊断是否流向真人问诊-条件组规则", example = "")
    private List<ConditionGroup> presAutoInquiryToRealForMultiDiagnoseConditions;

    // 多诊断是否流向真人问诊配置扩展
    public Map<String, Object> presAutoInquiryToRealForMultiDiagnoseExt() {
        return presAutoInquiryToRealForMultiDiagnose == null ? null : new HashMap<>() {{
            put("presAutoInquiryToRealForMultiDiagnoseRatio", presAutoInquiryToRealForMultiDiagnoseRatio);
            put("presAutoInquiryToRealForMultiDiagnoseConditions", presAutoInquiryToRealForMultiDiagnoseConditions);
        }};
    }


    @Schema(description = "特定年龄区间是否流向真人问诊", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    // @NotNull(message = "特定年龄区间是否流向真人问诊不能为空")
    private Boolean presAutoInquiryToRealForSpecialAgeRange;

    @Schema(description = "特定年龄区间是否流向真人问诊-流入比例", requiredMode = Schema.RequiredMode.REQUIRED, example = "50")
    @Max(value = 100, message = "特定年龄区间是否流向真人问诊-流入比例不能超过100")
    @Min(value = 0, message = "特定年龄区间是否流向真人问诊-流入比例不能小于0")
    private Integer presAutoInquiryToRealForSpecialAgeRangeRatio;

    @Schema(description = "特定年龄区间是否流向真人问诊-条件组规则", example = "")
    private List<ConditionGroup> presAutoInquiryToRealForSpecialAgeRangeConditions;

    // 特定年龄区间是否流向真人问诊配置扩展
    public Map<String, Object> presAutoInquiryToRealForSpecialAgeRangeExt() {
        return presAutoInquiryToRealForSpecialAgeRange == null ? null : new HashMap<>() {{
            put("presAutoInquiryToRealForSpecialAgeRangeRatio", presAutoInquiryToRealForSpecialAgeRangeRatio);
            put("presAutoInquiryToRealForSpecialAgeRangeConditions", presAutoInquiryToRealForSpecialAgeRangeConditions);
        }};
    }


    @Schema(description = "自动开方是否回流真人", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    // @NotNull(message = "自动开方是否回流真人不能为空")
    private Boolean presAutoInquiryReturnToReal;

    @Schema(description = "自动开方是否回流真人-积压数量", example = "1000")
    @Min(value = 0, message = "积压数量不能小于0")
    private Integer presAutoInquiryReturnToRealBacklogNum;

    @Schema(description = "自动开方是否回流真人-回流比例", example = "50")
    @Max(value = 100, message = "回流比例不能超过100")
    @Min(value = 0, message = "回流比例不能小于0")
    private Integer presAutoInquiryReturnToRealRatio;

    // 自动开方是否回流真人配置扩展
    public Map<String, Object> presAutoInquiryReturnToRealExt() {
        return presAutoInquiryReturnToReal == null ? null : new HashMap<>() {{
            put("presAutoInquiryReturnToRealBacklogNum", presAutoInquiryReturnToRealBacklogNum);
            put("presAutoInquiryReturnToRealRatio", presAutoInquiryReturnToRealRatio);
        }};
    }

}