package com.xyy.saas.inquiry.drugstore.api.option.dto;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import com.xyy.saas.inquiry.pojo.condition.ConditionGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

@Schema(description = "管理后台 - 全局-问诊配置选项 Response VO")
@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquiryOptionGlobalConfigRespDto implements Serializable {

    // 问诊流程配置-问诊合规配置
    /**
     * 问诊合规配置
     */
    private Boolean procInquiryCompliance;

    /**
     * 问诊合规配置-患者年龄限制-大于等于
     */
    private Integer procInquiryComplianceForPatientAgeGe;

    /**
     * 问诊合规配置-患者年龄限制-且小于
     */
    private Integer procInquiryComplianceForPatientAgeLt;

    /**
     * 问诊合规配置-妊娠哺乳是否可发起问诊
     */
    private Boolean procInquiryComplianceAllowForPregnancyLactation;

    // 问诊流程配置-图文交互对话弹出速度
    /**
     * 图文交互对话弹出速度
     */
    private Integer procTeletextInteractionDialogPopupSpeed;

    // 问诊流程配置-问诊服务开关
    /**
     * 问诊服务开关
     */
    private Boolean procInquiryServiceSwitch;

    // 问诊流程配置-医生接诊默认页面
    /**
     * 医生接诊默认页面
     */
    private String procDoctorAdmissionDefaultPage;

    // 医生开方配置-开方基础属性配置
    /**
     * 全局处方日期格式
     */
    private String presGlobalDateFormat;

    /**
     * 图文问诊医生可同时最大接单数量
     */
    private Integer presTeletextInquiryDoctorOrderMax;

    /**
     * 医生开方二次确认弹窗
     */
    private Boolean presDoctor2ndConfirmDialog;

    /**
     * 处方签名使用时间间隔
     */
    private Integer presSignatureUseInterval;

    /**
     * 医生不可接诊区域
     */
    private String presDoctorCannotInquiryArea;

    /**
     * 过敏史是否流向真人问诊
     */
    private Boolean presAutoInquiryToRealForAllergic;

    /**
     * 肝、肾功能异常是否流向真人问诊
     */
    private Boolean presAutoInquiryToRealForLiverRenalDysfunction;

    /**
     * 妊娠、哺乳期是否流向真人问诊
     */
    private Boolean presAutoInquiryToRealForPregnancyLactation;


    /**
     * 多诊断是否流向真人问诊
     */
    private Boolean presAutoInquiryToRealForMultiDiagnose;

    /**
     * 多诊断是否流向真人问诊-流入比例
     */
    private Integer presAutoInquiryToRealForMultiDiagnoseRatio;

    /**
     * 多诊断是否流向真人问诊-条件组规则
     */
    private List<ConditionGroup> presAutoInquiryToRealForMultiDiagnoseConditions;


    /**
     * 特定年龄区间是否流向真人问诊
     */
    private Boolean presAutoInquiryToRealForSpecialAgeRange;

    /**
     * 特定年龄区间是否流向真人问诊-流入比例
     */
    private Integer presAutoInquiryToRealForSpecialAgeRangeRatio;

    /**
     * 特定年龄区间是否流向真人问诊-条件组规则
     */
    private List<ConditionGroup> presAutoInquiryToRealForSpecialAgeRangeConditions;


    /**
     * 自动开方是否回流真人
     */
    private Boolean presAutoInquiryReturnToReal;

    /**
     * 自动开方是否回流真人-积压数量
     */
    private Integer presAutoInquiryReturnToRealBacklogNum;

    /**
     * 自动开方是否回流真人-回流比例
     */
    private Integer presAutoInquiryReturnToRealRatio;

    /**
     * 重复药品开关
     */
    private Boolean procRepeatDrugSwitch;


    public InquiryOptionGlobalConfigRespDto extToThis(String ext) {
        if (StringUtils.isBlank(ext)) {
            return this;
        }
        InquiryOptionGlobalConfigRespDto extObject = JsonUtils.parseObject(ext, InquiryOptionGlobalConfigRespDto.class);
        if (extObject == null) {
            return this;
        }
        // null属性忽略
        BeanUtil.copyProperties(extObject, this, CopyOptions.create().ignoreNullValue());
        return this;
    }

}