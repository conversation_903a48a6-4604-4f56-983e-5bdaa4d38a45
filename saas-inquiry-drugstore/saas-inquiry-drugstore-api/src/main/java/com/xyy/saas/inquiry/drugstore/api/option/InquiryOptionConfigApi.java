package com.xyy.saas.inquiry.drugstore.api.option;

import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigQueryDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.pojo.TenantDto;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public interface InquiryOptionConfigApi {

    /**
     * 全局-获得问诊配置选项
     *
     * @return 问诊配置选项
     */
    InquiryOptionGlobalConfigRespDto getInquiryOptionGlobalConfig(InquiryOptionConfigQueryDto queryDto);

    /**
     * 查询问诊配置选项 （不传 optionTypeEnums 则默认查询所有配置）
     *
     * @return 门店 区域 全局 问诊配置选项
     */
    InquiryOptionConfigRespDto getInquiryOptionConfig(TenantDto tenantDto, InquiryOptionTypeEnum... optionTypeEnums);

    /**
     * 查询问诊配置选项
     *
     * @param targetId       区域编码或者租户id
     * @param optionTypeEnum 类型
     * @return
     */
    String getInquiryOptionValue(Long targetId, InquiryOptionTypeEnum optionTypeEnum);


}
