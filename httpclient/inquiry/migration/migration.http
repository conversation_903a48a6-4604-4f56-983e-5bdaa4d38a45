### 请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "username": "develop",
  "password": "Abc123456"
}

> {%
  client.global.set("token", response.body.data.accessToken);
%}


### 2.迁移处方
POST {{baseAdminKernelUrl}}/kernel/common/history-prescription/migration
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "minId": 0,
  "maxId": 200000
}

### 2.查询处方
POST {{baseAdminKernelUrl}}/kernel/common/history-prescription/page-system
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "year": "2020"
}

### 2.查询处方详情
POST {{baseAdminKernelUrl}}/kernel/common/history-prescription/get
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "year": "2020",
  "id": 1753
}


### 2.系统导出
POST {{baseAdminKernelUrl}}/kernel/common/history-prescription/export-system
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "year": "2020"
}
