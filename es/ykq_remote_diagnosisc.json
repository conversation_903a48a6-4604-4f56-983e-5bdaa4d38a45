{"settings": {"index": {"refresh_interval": "1s", "number_of_shards": "1", "max_result_window": "1000000", "analysis": {"analyzer": {"comma_analyzer": {"type": "custom", "tokenizer": "comma_tokenizer"}, "diag_analyzer": {"type": "custom", "tokenizer": "diag_tokenizer"}}, "tokenizer": {"comma_tokenizer": {"pattern": ",", "type": "pattern"}, "diag_tokenizer": {"pattern": "&", "type": "pattern"}}}, "number_of_replicas": "1"}}, "mappings": {"properties": {"id": {"type": "long"}, "description": {"type": "keyword"}, "diagnosisPref": {"type": "keyword"}, "updateUser": {"type": "keyword"}, "updateTime": {"format": "yyyy-MM-dd HH:mm:ss", "type": "date"}, "diagnosisCodes": {"analyzer": "diag_analyzer", "type": "text"}, "commonProducts": {"analyzer": "comma_analyzer", "type": "text"}, "createTime": {"format": "yyyy-MM-dd HH:mm:ss", "type": "date"}, "yn": {"type": "byte"}, "createUser": {"type": "keyword"}, "categories": {"analyzer": "comma_analyzer", "type": "text"}, "caution": {"type": "integer"}, "status": {"type": "byte"}}}}