package com.xyy.saas.inquiry.product.api.catalog.dto.rule;

import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 */
@Data
public class MatchRule implements Serializable {

    /**
     * 源字段
     */
    @Schema(description = "源字段")
    private String sourceField;
    /**
     * 源字段名称
     */
    @Schema(description = "源字段名称")
    private String sourceFieldName;

    /**
     * 匹配关系 {@link MatchRuleOp}
     */
    @Schema(description = "匹配关系: eq-等于, has-包含, in-属于")
    private String op;

    /**
     * 目标字段
     */
    @Schema(description = "目标字段")
    private String targetField;
    /**
     * 目标字段名称
     */
    @Schema(description = "目标字段名称")
    private String targetFieldName;

}
