package com.xyy.saas.inquiry.product.api;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductPageQueryDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductSearchDto;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public interface ProductStdlibApi {

    /**
     * 查询自建标准库商品
     *
     * @param searchDto 查询条件
     * @return 自建标准库商品列表
     */
    List<ProductStdlibDto> searchProductStdlibList(StdlibProductSearchDto searchDto, int limit);

    /**
     * 查询自建标准库商品（分页）
     *
     * @param pageQueryDto 查询条件
     * @return 自建标准库商品列表
     */
    PageResult<ProductStdlibDto> queryProductStdlibPage(StdlibProductPageQueryDto pageQueryDto);
}
