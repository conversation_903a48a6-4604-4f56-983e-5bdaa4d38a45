package com.xyy.saas.inquiry.product.api.mid.dto;

import lombok.Data;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: xucao
 * @DateTime: 2025/8/8 13:44
 * @Description: 六级分类dto
 **/
@Data
public class SaasCategoryDto implements Serializable {

    private Integer id;
    private String title;
    private Integer priority;
    private Integer level;
    private Integer parentId;
    private List<SaasCategoryDto> children = new ArrayList<>();
}
