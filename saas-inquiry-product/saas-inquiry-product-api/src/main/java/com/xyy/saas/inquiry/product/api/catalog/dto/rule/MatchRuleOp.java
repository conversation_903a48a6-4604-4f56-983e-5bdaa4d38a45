package com.xyy.saas.inquiry.product.api.catalog.dto.rule;

import java.util.Arrays;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 *
 */
@Getter
public enum MatchRuleOp {
    EQUALS("equals", "等于"),
    HAS("has", "包含"),
    IN("in", "属于"),
    ;

    private final String type;
    private final String desc;

    MatchRuleOp(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static MatchRuleOp fromType(String type) {
        return Arrays.stream(values())
            .filter(t -> StringUtils.equalsIgnoreCase(t.getType(), type))
            .findFirst()
            .orElse(null);
    }
}
