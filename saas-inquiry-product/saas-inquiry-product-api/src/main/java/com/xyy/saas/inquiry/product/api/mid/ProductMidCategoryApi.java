package com.xyy.saas.inquiry.product.api.mid;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.api.mid.dto.SaasCategoryDto;
import java.util.List;
import java.util.Map;

/**
 * @Author: xucao
 * @DateTime: 2025/8/8 13:42
 * @Description: 商品中台六级分类相关接口
 **/
public interface ProductMidCategoryApi {

    /**
     * 通过上级 id 获取子级分类
     * @param parentCategoryId 传空或者 0 获取第一级分类
     * @return list
     */
    List<SaasCategoryDto> queryCategoryByParentId(Integer parentCategoryId);


    /**
     * 通过商品名称获取商品六级分类
     * @param productName  商品名称
     * @return list
     */
    List<List<SaasCategoryDto>> queryProductCategoryByProductName(String productName);


    /**
     *  通过商品名称获取商品六级分类
     * @param productNames 商品名称列表
     * @return map
     */
    Map<String, List<List<SaasCategoryDto>>> queryProductCategoryByProductNameList(List<String> productNames);


    /**
     * 根据id 查询分类链路
     * @param endIds 末级分类id
     * @return
     */
    Map<Integer, List<List<SaasCategoryDto>>> queryCategoryPathByIds(List<Integer> endIds);

    /**
     *  分类名称模糊匹配上级链路 - 分页
     * @param name
     * @param pageNum 从0开始
     * @param pageSize
     * @return
     */
    PageResult<List<SaasCategoryDto>> queryCategoryPathByNameFuzzy(String name, Integer pageNum, Integer pageSize);


    /**
     * 根据六级分类名称查看分类链路
     * @param names
     * @return
     */
    Map<String, List<List<SaasCategoryDto>>> queryCategoryPathByNames(List<String> names);
}
