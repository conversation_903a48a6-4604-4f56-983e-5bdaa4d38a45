package com.xyy.saas.inquiry.product.server.util;

import lombok.extern.slf4j.Slf4j;

/**
 * 并发处理性能测试工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class ConcurrentPerformanceTestUtil {

    /**
     * 性能测试对比结果
     */
    public static class PerformanceResult {
        private final long normalTimeMs;
        private final long concurrentTimeMs;
        private final int normalResult;
        private final int concurrentResult;
        private final double speedupRatio;

        public PerformanceResult(long normalTimeMs, long concurrentTimeMs, 
                               int normalResult, int concurrentResult) {
            this.normalTimeMs = normalTimeMs;
            this.concurrentTimeMs = concurrentTimeMs;
            this.normalResult = normalResult;
            this.concurrentResult = concurrentResult;
            this.speedupRatio = concurrentTimeMs > 0 ? (double) normalTimeMs / concurrentTimeMs : 0;
        }

        public void printResult() {
            log.info("=== 性能测试对比结果 ===");
            log.info("普通方法耗时: {}ms, 处理数量: {}", normalTimeMs, normalResult);
            log.info("并发方法耗时: {}ms, 处理数量: {}", concurrentTimeMs, concurrentResult);
            log.info("性能提升倍数: {:.2f}x", speedupRatio);
            
            if (speedupRatio > 1.5) {
                log.info("✅ 并发优化效果显著，性能提升 {:.1f}%", (speedupRatio - 1) * 100);
            } else if (speedupRatio > 1.0) {
                log.info("⚠️ 并发优化有轻微提升，性能提升 {:.1f}%", (speedupRatio - 1) * 100);
            } else {
                log.info("❌ 并发优化未显示性能提升，可能需要调整参数或数据量太小");
            }
        }

        // Getters
        public long getNormalTimeMs() { return normalTimeMs; }
        public long getConcurrentTimeMs() { return concurrentTimeMs; }
        public int getNormalResult() { return normalResult; }
        public int getConcurrentResult() { return concurrentResult; }
        public double getSpeedupRatio() { return speedupRatio; }
    }

    /**
     * 执行性能对比测试
     *
     * @param normalTask 普通任务
     * @param concurrentTask 并发任务
     * @param taskDescription 任务描述
     * @return 性能对比结果
     */
    public static PerformanceResult comparePerformance(
            java.util.function.Supplier<Integer> normalTask,
            java.util.function.Supplier<Integer> concurrentTask,
            String taskDescription) {
        
        log.info("开始性能对比测试: {}", taskDescription);
        
        // 预热
        log.info("预热阶段...");
        try {
            normalTask.get();
            concurrentTask.get();
        } catch (Exception e) {
            log.warn("预热阶段出现异常（正常）: {}", e.getMessage());
        }
        
        // 测试普通方法
        log.info("测试普通方法...");
        long normalStartTime = System.currentTimeMillis();
        int normalResult = 0;
        try {
            normalResult = normalTask.get();
        } catch (Exception e) {
            log.error("普通方法执行异常", e);
        }
        long normalEndTime = System.currentTimeMillis();
        long normalTimeMs = normalEndTime - normalStartTime;
        
        // 等待一段时间确保系统稳定
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 测试并发方法
        log.info("测试并发方法...");
        long concurrentStartTime = System.currentTimeMillis();
        int concurrentResult = 0;
        try {
            concurrentResult = concurrentTask.get();
        } catch (Exception e) {
            log.error("并发方法执行异常", e);
        }
        long concurrentEndTime = System.currentTimeMillis();
        long concurrentTimeMs = concurrentEndTime - concurrentStartTime;
        
        PerformanceResult result = new PerformanceResult(
            normalTimeMs, concurrentTimeMs, normalResult, concurrentResult);
        result.printResult();
        
        return result;
    }

    /**
     * 生成性能测试建议
     *
     * @param result 性能测试结果
     * @return 优化建议
     */
    public static String generateOptimizationSuggestions(PerformanceResult result) {
        StringBuilder suggestions = new StringBuilder();
        
        suggestions.append("=== 性能优化建议 ===\n");
        
        if (result.getSpeedupRatio() < 1.0) {
            suggestions.append("1. 并发方法性能未达预期，建议检查：\n");
            suggestions.append("   - 数据量是否足够大（建议 > 10000 条）\n");
            suggestions.append("   - 线程池配置是否合理\n");
            suggestions.append("   - 是否存在资源竞争（如数据库连接池）\n");
        } else if (result.getSpeedupRatio() < 1.5) {
            suggestions.append("1. 并发方法有轻微提升，可以进一步优化：\n");
            suggestions.append("   - 调整批次大小（当前可能过小或过大）\n");
            suggestions.append("   - 优化线程池配置\n");
            suggestions.append("   - 考虑增加数据量进行测试\n");
        } else {
            suggestions.append("1. 并发方法性能优异，建议：\n");
            suggestions.append("   - 在生产环境中使用并发版本\n");
            suggestions.append("   - 监控实际运行效果\n");
            suggestions.append("   - 根据服务器配置调整线程池参数\n");
        }
        
        suggestions.append("\n2. 通用建议：\n");
        suggestions.append("   - 定期监控方法执行时间和资源使用\n");
        suggestions.append("   - 根据数据量和服务器配置选择合适的方法\n");
        suggestions.append("   - 建立性能基准线用于后续优化\n");
        
        return suggestions.toString();
    }
}