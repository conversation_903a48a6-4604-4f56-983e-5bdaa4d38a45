package com.xyy.saas.inquiry.product.server.service.catalog.util;

import com.xyy.saas.inquiry.product.api.catalog.dto.rule.MatchRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 匹配规则比较工具类
 */
@Slf4j
@Component
public class MatchRuleComparator {

    /**
     * 比较西药匹配规则是否完全一致
     *
     * @param oldRules 旧版本西药匹配规则
     * @param newRules 新版本西药匹配规则
     * @return true-规则完全一致，false-规则有变更
     */
    public boolean compareWesternMedicineRules(List<MatchRule> oldRules, List<MatchRule> newRules) {
        return compareMatchRules(oldRules, newRules, "西药");
    }

    /**
     * 比较中药匹配规则是否完全一致
     *
     * @param oldRules 旧版本中药匹配规则
     * @param newRules 新版本中药匹配规则
     * @return true-规则完全一致，false-规则有变更
     */
    public boolean compareChineseMedicineRules(List<MatchRule> oldRules, List<MatchRule> newRules) {
        return compareMatchRules(oldRules, newRules, "中药");
    }

    /**
     * 比较匹配规则是否完全一致的核心方法
     *
     * @param oldRules 旧版本匹配规则
     * @param newRules 新版本匹配规则
     * @param medicineType 药品类型（用于日志）
     * @return true-规则完全一致，false-规则有变更
     */
    private boolean compareMatchRules(List<MatchRule> oldRules, List<MatchRule> newRules, String medicineType) {
        // 都为空，视为一致
        if (CollectionUtils.isEmpty(oldRules) && CollectionUtils.isEmpty(newRules)) {
            log.debug("[compareMatchRules]{}匹配规则都为空，视为一致", medicineType);
            return true;
        }

        // 一个为空一个不为空，不一致
        if (CollectionUtils.isEmpty(oldRules) || CollectionUtils.isEmpty(newRules)) {
            log.info("[compareMatchRules]{}匹配规则数量不一致，旧版本: {}, 新版本: {}", 
                medicineType, 
                oldRules != null ? oldRules.size() : 0,
                newRules != null ? newRules.size() : 0);
            return false;
        }

        // 数量不一致
        if (oldRules.size() != newRules.size()) {
            log.info("[compareMatchRules]{}匹配规则数量不一致，旧版本: {}, 新版本: {}", 
                medicineType, oldRules.size(), newRules.size());
            return false;
        }

        // 将规则转换为Set进行比较
        Set<String> oldRuleSignatures = new HashSet<>();
        for (MatchRule rule : oldRules) {
            oldRuleSignatures.add(generateRuleSignature(rule));
        }

        Set<String> newRuleSignatures = new HashSet<>();
        for (MatchRule rule : newRules) {
            newRuleSignatures.add(generateRuleSignature(rule));
        }

        // 比较规则签名集合是否完全一致
        boolean isEqual = oldRuleSignatures.equals(newRuleSignatures);
        
        if (isEqual) {
            log.debug("[compareMatchRules]{}匹配规则完全一致", medicineType);
        } else {
            log.info("[compareMatchRules]{}匹配规则有变更", medicineType);
            // 记录具体的差异
            Set<String> onlyInOld = new HashSet<>(oldRuleSignatures);
            onlyInOld.removeAll(newRuleSignatures);
            
            Set<String> onlyInNew = new HashSet<>(newRuleSignatures);
            onlyInNew.removeAll(oldRuleSignatures);
            
            if (!onlyInOld.isEmpty()) {
                log.info("[compareMatchRules]{}匹配规则 - 旧版本独有: {}", medicineType, onlyInOld);
            }
            if (!onlyInNew.isEmpty()) {
                log.info("[compareMatchRules]{}匹配规则 - 新版本独有: {}", medicineType, onlyInNew);
            }
        }

        return isEqual;
    }

    /**
     * 生成匹配规则的唯一签名
     * 用于比较规则是否完全一致
     *
     * @param rule 匹配规则
     * @return 规则签名字符串
     */
    private String generateRuleSignature(MatchRule rule) {
        if (rule == null) {
            return "";
        }

        return String.format("%s|%s|%s",
            StringUtils.defaultString(rule.getSourceField()),
            StringUtils.defaultString(rule.getOp()),
            StringUtils.defaultString(rule.getTargetField()));
    }

    /**
     * 从匹配规则中提取所有的目标字段
     * 用于后续的字段变更检测
     *
     * @param matchRules 匹配规则列表
     * @return 目标字段集合
     */
    public Set<String> extractTargetFields(List<MatchRule> matchRules) {
        Set<String> targetFields = new HashSet<>();
        
        if (CollectionUtils.isNotEmpty(matchRules)) {
            for (MatchRule rule : matchRules) {
                if (StringUtils.isNotBlank(rule.getTargetField())) {
                    targetFields.add(rule.getTargetField());
                }
            }
        }
        
        return targetFields;
    }
}