package com.xyy.saas.inquiry.product.server.api.product;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.api.ProductStdlibApi;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductPageQueryDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductSearchDto;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductPageQueryVo;
import com.xyy.saas.inquiry.product.server.convert.product.ProductConvert;
import com.xyy.saas.inquiry.product.server.service.product.ProductStdlibService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@DubboService
public class ProductStdlibApiImpl implements ProductStdlibApi {

    @Resource
    private ProductStdlibService productStdlibService;

    /**
     * 查询自建标准库商品
     *
     * @param searchDto 查询条件
     * @return 去重后的商品通用名列表
     */
    @Override
    public List<ProductStdlibDto> searchProductStdlibList(StdlibProductSearchDto searchDto, int limit) {
        return productStdlibService.searchStdlibProductList(searchDto, limit);
    }

    /**
     * 查询自建标准库商品（分页）
     *
     * @param pageQueryDto 查询条件
     * @return 自建标准库商品列表
     */
    @Override
    public PageResult<ProductStdlibDto> queryProductStdlibPage(StdlibProductPageQueryDto pageQueryDto) {
        StdlibProductPageQueryVo pageQueryVo = ProductConvert.INSTANCE.StdlibProductPageQueryDto2Vo(pageQueryDto);
        return productStdlibService.getSelfStdlibProductPage(pageQueryVo);
    }
}
