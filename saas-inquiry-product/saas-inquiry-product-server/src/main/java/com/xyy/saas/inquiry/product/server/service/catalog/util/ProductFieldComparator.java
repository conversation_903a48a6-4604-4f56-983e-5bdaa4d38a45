package com.xyy.saas.inquiry.product.server.service.catalog.util;

import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.MedicalCatalogDetailDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Objects;
import java.util.Set;

/**
 * 药品字段变化检测工具类
 * 用于检测相同项目编码的药品在不同版本间的字段是否发生变化
 */
@Slf4j
@Component
public class ProductFieldComparator {

    /**
     * 比较两个药品的指定字段是否发生变化
     * 
     * @param currentProduct 当前版本药品
     * @param previousProduct 上一版本药品
     * @param targetFields 需要比较的目标字段集合
     * @return true-字段无变化，false-字段有变化
     */
    public boolean compareProductFields(MedicalCatalogDetailDO currentProduct, 
                                      MedicalCatalogDetailDO previousProduct, 
                                      Set<String> targetFields) {
        if (currentProduct == null || previousProduct == null) {
            log.warn("[compareProductFields]药品对象为空，无法比较");
            return false;
        }

        if (CollectionUtils.isEmpty(targetFields)) {
            log.debug("[compareProductFields]目标字段为空，视为无变化");
            return true;
        }

        // 项目编码必须相同才能比较
        if (!Objects.equals(currentProduct.getProjectCode(), previousProduct.getProjectCode())) {
            log.warn("[compareProductFields]项目编码不一致，当前: {}, 历史: {}", 
                currentProduct.getProjectCode(), previousProduct.getProjectCode());
            return false;
        }

        boolean hasChanges = false;
        StringBuilder changesLog = new StringBuilder();

        for (String fieldName : targetFields) {
            try {
                Object currentValue = currentProduct.getFieldValue(fieldName);
                Object previousValue = previousProduct.getFieldValue(fieldName);

                // 将null转换为空字符串进行比较
                String currentStr = currentValue != null ? currentValue.toString() : "";
                String previousStr = previousValue != null ? previousValue.toString() : "";

                if (!Objects.equals(currentStr, previousStr)) {
                    hasChanges = true;
                    if (!changesLog.isEmpty()) {
                        changesLog.append(", ");
                    }
                    changesLog.append(String.format("%s: '%s' -> '%s'", fieldName, previousStr, currentStr));
                }
            } catch (Exception e) {
                log.error("[compareProductFields]比较字段失败, fieldName: {}, projectCode: {}", 
                    fieldName, currentProduct.getProjectCode(), e);
                // 字段比较失败视为有变化，采用保守策略
                hasChanges = true;
            }
        }

        if (hasChanges) {
            log.debug("[compareProductFields]项目编码 {} 的字段发生变化: {}", 
                currentProduct.getProjectCode(), changesLog.toString());
        } else {
            log.debug("[compareProductFields]项目编码 {} 的比较字段无变化", currentProduct.getProjectCode());
        }

        return !hasChanges;
    }

    /**
     * 检查药品的核心信息是否发生变化
     * 包括项目名称、品牌名称、规格、生产企业、批准文号等核心字段
     *
     * @param currentProduct 当前版本药品
     * @param previousProduct 上一版本药品  
     * @return true-核心信息无变化，false-核心信息有变化
     */
    public boolean compareCoreProductFields(MedicalCatalogDetailDO currentProduct, 
                                          MedicalCatalogDetailDO previousProduct) {
        if (currentProduct == null || previousProduct == null) {
            return false;
        }

        // 检查核心字段
        return Objects.equals(StringUtils.defaultString(currentProduct.getProjectName()), 
                            StringUtils.defaultString(previousProduct.getProjectName()))
            && Objects.equals(StringUtils.defaultString(currentProduct.getBrandName()), 
                            StringUtils.defaultString(previousProduct.getBrandName()))
            && Objects.equals(StringUtils.defaultString(currentProduct.getSpec()), 
                            StringUtils.defaultString(previousProduct.getSpec()))
            && Objects.equals(StringUtils.defaultString(currentProduct.getManufacturer()), 
                            StringUtils.defaultString(previousProduct.getManufacturer()))
            && Objects.equals(StringUtils.defaultString(currentProduct.getApprovalNumber()), 
                            StringUtils.defaultString(previousProduct.getApprovalNumber()));
    }
}