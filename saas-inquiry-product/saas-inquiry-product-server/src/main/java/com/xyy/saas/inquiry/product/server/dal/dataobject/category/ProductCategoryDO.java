package com.xyy.saas.inquiry.product.server.dal.dataobject.category;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 商品六级分类 DO
 *
 * <AUTHOR>
 */
@TableName("saas_product_category")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductCategoryDO extends BaseDO {

    public static final Long PARENT_DICT_ID_ROOT = 0L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 分类名称
     */
    private String name;
    /**
     * 字典id（中台）
     */
    private Long dictId;
    /**
     * 字典父id（中台）
     */
    private Long parentDictId;
    /**
     * 排序权重
     */
    private Integer sortOrder;
    /**
     * 备注
     */
    private String remark;

    /**
     * 检查分类是否有更新
     * @param oldCategory
     * @return
     */
    public boolean checkIsChanged(ProductCategoryDO oldCategory) {
        if (oldCategory == null) {
            return true;
        }
        return (this.name != null && !Objects.equals(this.name, oldCategory.getName())) ||
            (this.dictId != null && !Objects.equals(this.dictId, oldCategory.getDictId())) ||
            (this.parentDictId != null && !Objects.equals(this.parentDictId, oldCategory.getParentDictId())) ||
            (this.sortOrder != null && !Objects.equals(this.sortOrder, oldCategory.getSortOrder())) ||
            (this.remark != null && !Objects.equals(this.remark, oldCategory.getRemark()));
    }
}