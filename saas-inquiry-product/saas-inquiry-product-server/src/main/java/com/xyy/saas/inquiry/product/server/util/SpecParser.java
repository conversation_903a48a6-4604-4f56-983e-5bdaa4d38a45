package com.xyy.saas.inquiry.product.server.util;


import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/// 规格解析器，用于从规格字符串中提取最小包装数。
///
/// 1. 匹配 "数字+可数单位" 的正则表达式：(\d+)(s|片|粒|丸|颗|袋|支|板|小盒|盒|瓶|贴|包|张|块|罐)
/// 2. 按 * 切割规格字符串，每部分用正则提取 "数字+可数单位"
/// 3. 将所有匹配的数字提取出来，作为乘法因子
///
/// 示例：
/// - 输入 "10s*2板*3小盒"，输出 60。
/// - 输入 "47.5mg*14s*2板 薄膜衣"，输出 28。
/// - 输入 "5cm*6cm*2贴*4片*2袋"，输出 16。
///
/// <AUTHOR>
/// @version 1.0
/// @since 1.0
@Component
public class SpecParser {

    // 匹配 "数字+可数单位" 的正则表达式
    private static Pattern NUMBER_UNIT_PATTERN;
    
    // 静态实例，用于访问配置
    private static SpecParser instance;
    
    @Value("${spec-parser.number-unit-pattern:(\\d+)(s|S|片|粒|丸|颗|袋|支|板|小盒|盒|瓶|贴|包|张|块|罐)}")
    private String numberUnitPattern;
    
    @PostConstruct
    public void init() {
        NUMBER_UNIT_PATTERN = Pattern.compile(numberUnitPattern);
        instance = this;
    }


    /**
     * 根据图片中的规则，从规格字符串中清洗出最小包装数
     * @param spec 规格字符串，例如 "10s*2板*3小盒"
     * @return 计算出的最小包装数量
     */
    public static Integer parseMinimumPackageQuantity(String spec) {
        // 对于空或无效输入，默认返回null
        if (spec == null || spec.trim().isEmpty()) {
            return null;
        }

        // 预处理规格字符串，去除多余的空格
        List<Integer> factors = getFactors(spec);

        // 如果没有找到任何有效的乘法因子，返回1
        if (factors.isEmpty()) {
            return 1;
        }

        // 使用Java Stream将所有因子相乘
        return factors.stream().reduce(1, (a, b) -> a * b);
    }

    /**
     * 从规格字符串中提取所有有效的乘法因子
     * @param spec 规格字符串
     * @return 乘法因子列表
     */
    private static List<Integer> getFactors(String spec) {
        // 获取配置的Pattern，如果未初始化则使用默认值
        Pattern pattern = getPattern();
        
        String[] parts = spec.split("\\*");
        List<Integer> factors = new ArrayList<>();

        for (String part : parts) {
            Matcher matcher = pattern.matcher(part.trim());
            if (matcher.find()) {
                factors.add(Integer.parseInt(matcher.group(1)));
            }
        }
        return factors;
    }
    
    /**
     * 获取匹配模式，支持配置和默认值
     * @return Pattern对象
     */
    private static Pattern getPattern() {
        if (NUMBER_UNIT_PATTERN != null) {
            return NUMBER_UNIT_PATTERN;
        }
        
        // 如果静态Pattern未初始化，尝试从实例获取
        if (instance != null && instance.numberUnitPattern != null) {
            NUMBER_UNIT_PATTERN = Pattern.compile(instance.numberUnitPattern);
            return NUMBER_UNIT_PATTERN;
        }
        
        // 最后的备选方案：使用硬编码的默认值
        return Pattern.compile("(\\d+)(s|S|片|粒|丸|颗|袋|支|板|小盒|盒|瓶|贴|包|张|块|罐)");
    }

}
