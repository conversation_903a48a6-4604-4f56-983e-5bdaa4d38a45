package com.xyy.saas.inquiry.product.server.api.mid;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.api.mid.ProductMidCategoryApi;
import com.xyy.saas.inquiry.product.api.mid.dto.SaasCategoryDto;
import com.xyy.saas.inquiry.product.server.service.product.mid.ProductMidStdlibService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import java.util.List;
import java.util.Map;

/**
 * @Author: xucao
 * @DateTime: 2025/8/8 13:53
 * @Description: 商品中台六级分类相关api
 **/
@DubboService
public class ProductMidCategoryApiImpl implements ProductMidCategoryApi {

    @Resource
    private ProductMidStdlibService productMidStdlibService;

    /**
     * 通过上级 id 获取子级分类
     *
     * @param parentCategoryId 传空或者 0 获取第一级分类
     * @return list
     */
    @Override
    public List<SaasCategoryDto> queryCategoryByParentId(Integer parentCategoryId) {
        return productMidStdlibService.queryCategoryByParentId(parentCategoryId);
    }

    /**
     * 通过商品名称获取商品六级分类
     *
     * @param productName 商品名称
     * @return list
     */
    @Override
    public List<List<SaasCategoryDto>> queryProductCategoryByProductName(String productName) {
        return productMidStdlibService.queryProductCategoryByProductName(productName);
    }

    /**
     * 通过商品名称获取商品六级分类
     *
     * @param productNames 商品名称列表
     * @return map
     */
    @Override
    public Map<String, List<List<SaasCategoryDto>>> queryProductCategoryByProductNameList(List<String> productNames) {
        return productMidStdlibService.queryProductCategoryByProductNameList(productNames);
    }

    /**
     * 根据id 查询分类链路
     *
     * @param endIds 末级分类id
     * @return
     */
    @Override
    public Map<Integer, List<List<SaasCategoryDto>>> queryCategoryPathByIds(List<Integer> endIds) {
        return productMidStdlibService.queryCategoryPathByIds(endIds);
    }

    /**
     * 分类名称模糊匹配上级链路 - 分页
     *
     * @param name
     * @param pageNum  从0开始
     * @param pageSize
     * @return
     */
    @Override
    public PageResult<List<SaasCategoryDto>> queryCategoryPathByNameFuzzy(String name, Integer pageNum, Integer pageSize) {
        return productMidStdlibService.queryCategoryPathByNameFuzzy(name,pageNum,pageSize);
    }

    /**
     * 根据六级分类名称查看分类链路
     *
     * @param names
     * @return
     */
    @Override
    public Map<String, List<List<SaasCategoryDto>>> queryCategoryPathByNames(List<String> names) {
        return productMidStdlibService.queryCategoryPathByNames(names);
    }
}
