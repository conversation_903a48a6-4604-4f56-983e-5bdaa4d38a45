package com.xyy.saas.inquiry.product.server.service.catalog.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.MsearchRequest;
import co.elastic.clients.elasticsearch.core.MsearchResponse;
import co.elastic.clients.elasticsearch.core.msearch.MultiSearchItem;
import co.elastic.clients.elasticsearch.core.msearch.MultisearchBody;
import co.elastic.clients.elasticsearch.core.msearch.RequestItem;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.product.api.catalog.dto.CatalogExtDTO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.MedicalCatalogDetailDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibDO;
import com.xyy.saas.inquiry.product.server.service.catalog.CatalogStdlibMatchService;
import com.xyy.saas.inquiry.product.server.service.catalog.processor.MatchRuleProcessor;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * 目录标准库匹配服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CatalogStdlibMatchServiceImpl implements CatalogStdlibMatchService {

    private final ElasticsearchClient elasticsearchClient;
    private final  MatchRuleProcessor matchRuleProcessor;

    // ES索引名
    private static final String ES_INDEX = "saas_product_stdlib_index";
    
    // 并发查询的批次大小
    private static final int CONCURRENT_BATCH_SIZE = 100;

    // 并发查询线程池
    private final AtomicInteger stdlibMatchCounter = new AtomicInteger(1);
    private final Executor stdlibMatchExecutor = new ThreadPoolExecutor(
        10, 10, 60L, TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(1000),
        r -> new Thread(r, "stdlib-match-" + stdlibMatchCounter.getAndIncrement()),
        new ThreadPoolExecutor.CallerRunsPolicy()
    );

    @Override
    public Map<Long, Long> batchMatchCatalogToStdlib(List<MedicalCatalogDetailDO> catalogDetailList, CatalogExtDTO catalogExt) {
        if (CollectionUtils.isEmpty(catalogDetailList) || catalogExt == null || catalogExt.getMatchStdlib() != Boolean.TRUE) {
            return new HashMap<>();
        }

        long startTime = System.currentTimeMillis();
        log.info("[batchMatchCatalogToStdlib]开始批量匹配, 总数: {}", catalogDetailList.size());

        Map<Long, Long> matchResults = new HashMap<>();

        try {
            if (catalogDetailList.size() <= CONCURRENT_BATCH_SIZE) {
                // 小批量数据使用批量查询
                matchResults = processSmallBatch(catalogDetailList, catalogExt);
            } else {
                // 大批量数据使用并发批量处理
                matchResults = processConcurrentBatches(catalogDetailList, catalogExt);
            }
        } catch (Exception e) {
            log.error("[batchMatchCatalogToStdlib]批量匹配异常", e);
        }

        long endTime = System.currentTimeMillis();
        long executeTime = endTime - startTime;
        
        log.info("[batchMatchCatalogToStdlib]批量匹配完成, 总数: {}, 匹配成功: {}, 执行时间: {}ms", 
            catalogDetailList.size(), matchResults.size(), executeTime);

        return matchResults;
    }

    @Override
    public Long matchCatalogToStdlib(MedicalCatalogDetailDO catalogDetail, CatalogExtDTO catalogExt) {
        if (catalogDetail == null || catalogExt == null || catalogExt.getMatchStdlib() != Boolean.TRUE) {
            return null;
        }

        try {
            // 构建ES查询对象
            Query esQuery = matchRuleProcessor.buildEsQuery(catalogDetail, catalogExt);
            if (esQuery == null) {
                log.debug("[matchCatalogToStdlib]未构建到查询对象, catalogDetailId: {}", catalogDetail.getId());
                return null;
            }

            // 执行ES查询
            Long stdlibId = this.executeEsQuery(esQuery);
            
            if (stdlibId != null) {
                log.debug("[matchCatalogToStdlib]匹配成功, catalogDetailId: {}, stdlibId: {}", 
                    catalogDetail.getId(), stdlibId);
            } else {
                log.debug("[matchCatalogToStdlib]未匹配到标准库, catalogDetailId: {}, projectCode: {}", 
                    catalogDetail.getId(), catalogDetail.getProjectCode());
            }

            return stdlibId;
        } catch (Exception e) {
            log.error("[matchCatalogToStdlib]匹配异常, catalogDetailId: {}, projectCode: {}",
                catalogDetail.getId(), catalogDetail.getProjectCode(), e);
            return null;
        }
    }

    /**
     * 处理小批量数据
     */
    private Map<Long, Long> processSmallBatch(List<MedicalCatalogDetailDO> catalogDetailList, CatalogExtDTO catalogExt) {
        Map<Long, Long> matchResults = new HashMap<>();
        
        // 1. 构建所有查询对象
        List<Query> queryList = new ArrayList<>();
        List<MedicalCatalogDetailDO> validDetails = new ArrayList<>();
        
        for (MedicalCatalogDetailDO catalogDetail : catalogDetailList) {
            Query esQuery = matchRuleProcessor.buildEsQuery(catalogDetail, catalogExt);
            if (esQuery != null) {
                queryList.add(esQuery);
                validDetails.add(catalogDetail);
            }
        }
        
        if (queryList.isEmpty()) {
            log.debug("[processSmallBatch]没有有效的查询对象");
            return matchResults;
        }
        
        log.info("[processSmallBatch]开始批量查询, 查询数量: {}", queryList.size());
        
        // 2. 批量执行ES查询
        List<Long> stdlibIds = this.batchExecuteEsQuery(queryList);
        
        // 3. 组装结果
        for (int i = 0; i < validDetails.size() && i < stdlibIds.size(); i++) {
            Long stdlibId = stdlibIds.get(i);
            if (stdlibId != null) {
                matchResults.put(validDetails.get(i).getId(), stdlibId);
            }
        }
        
        log.info("[processSmallBatch]批量查询完成, 匹配成功: {} / {}", matchResults.size(), validDetails.size());
        return matchResults;
    }
    
    /**
     * 处理大批量数据（并发批量处理）
     */
    private Map<Long, Long> processConcurrentBatches(List<MedicalCatalogDetailDO> catalogDetailList, CatalogExtDTO catalogExt) {
        Map<Long, Long> totalResults = new HashMap<>();
        
        // 将数据分成多个批次
        List<List<MedicalCatalogDetailDO>> batches = Lists.partition(catalogDetailList, CONCURRENT_BATCH_SIZE);
        
        log.info("[processConcurrentBatches]开始并发批量处理, 总批次: {}, 每批大小: {}", batches.size(), CONCURRENT_BATCH_SIZE);
        
        // 并发处理每个批次
        List<CompletableFuture<Map<Long, Long>>> futures = new ArrayList<>();
        
        for (int i = 0; i < batches.size(); i++) {
            final int batchIndex = i;
            final List<MedicalCatalogDetailDO> batch = batches.get(i);
            
            CompletableFuture<Map<Long, Long>> future = CompletableFuture.supplyAsync(() -> {
                try {
                    log.debug("[processConcurrentBatches]处理批次 {}, 大小: {}", batchIndex + 1, batch.size());
                    return processSmallBatch(batch, catalogExt);
                } catch (Exception e) {
                    log.error("[processConcurrentBatches]批次 {} 处理异常", batchIndex + 1, e);
                    return new HashMap<>();
                }
            }, stdlibMatchExecutor);
            
            futures.add(future);
        }
        
        // 等待所有批次完成并合并结果
        for (int i = 0; i < futures.size(); i++) {
            try {
                Map<Long, Long> batchResult = futures.get(i).get();
                totalResults.putAll(batchResult);
                log.debug("[processConcurrentBatches]批次 {} 完成, 匹配数量: {}", i + 1, batchResult.size());
            } catch (Exception e) {
                log.error("[processConcurrentBatches]获取批次 {} 结果异常", i + 1, e);
            }
        }
        
        log.info("[processConcurrentBatches]并发批量处理完成, 总匹配数量: {}", totalResults.size());
        return totalResults;
    }



    /**
     * 批量执行ES查询
     *
     * @param queryList ES查询列表
     * @return 查询结果列表，索引对应查询列表的索引
     */
    public List<Long> batchExecuteEsQuery(List<Query> queryList) {
        if (CollectionUtils.isEmpty(queryList)) {
            return new ArrayList<>();
        }

        long startTime = System.currentTimeMillis();

        try {
            // 构建Multi-Search请求
            MsearchRequest.Builder msearchBuilder = new MsearchRequest.Builder();

            for (Query query : queryList) {
                RequestItem.Builder requestBuilder = new RequestItem.Builder()
                    .header(h -> h.index(ES_INDEX))
                    .body(MultisearchBody.of(s -> s
                        .query(query)
                        .from(0)
                        .size(1)
                        .source(source -> source.filter(f -> f.includes("id")))
                        .timeout("5s")
                    ));

                msearchBuilder.searches(requestBuilder.build());
            }

            // 执行Multi-Search
            MsearchResponse<ProductStdlibDO> response = elasticsearchClient.msearch(
                msearchBuilder.build(),
                ProductStdlibDO.class
            );

            // 解析结果
            List<Long> results = parseMultiSearchResponse(response, queryList.size());

            long executeTime = System.currentTimeMillis() - startTime;
            long matchedCount = results.stream().mapToLong(id -> id != null ? 1 : 0).sum();

            log.info("[batchExecuteEsQuery]批量ES查询完成, 查询数量: {}, 匹配数量: {}, 执行时间: {}ms, 平均耗时: {}ms/个",
                queryList.size(), matchedCount, executeTime, executeTime / queryList.size());

            return results;

        } catch (Exception e) {
            long executeTime = System.currentTimeMillis() - startTime;
            log.error("[batchExecuteEsQuery]批量ES查询异常, 查询数量: {}, 执行时间: {}ms", queryList.size(), executeTime, e);

            // 异常时返回对应数量的null值
            List<Long> emptyResults = new ArrayList<>();
            for (int i = 0; i < queryList.size(); i++) {
                emptyResults.add(null);
            }
            return emptyResults;
        }
    }

    /**
     * 解析Multi-Search响应
     */
    private List<Long> parseMultiSearchResponse(MsearchResponse<ProductStdlibDO> response, int expectedSize) {
        List<Long> results = new ArrayList<>();

        if (response == null || response.responses() == null) {
            for (int i = 0; i < expectedSize; i++) {
                results.add(null);
            };
            return results;
        }

        response.responses().forEach(item -> {
            Long stdlibId = null;
            if (item.isResult()) {
                MultiSearchItem<ProductStdlibDO> searchResponse = item.result();

                if (searchResponse != null &&
                    searchResponse.hits() != null &&
                    CollectionUtils.isNotEmpty(searchResponse.hits().hits())) {

                    ProductStdlibDO stdlib = searchResponse.hits().hits().getFirst().source();
                    if (stdlib != null && stdlib.getId() != null) {
                        stdlibId = stdlib.getId();
                    }
                }
            } else if (item.isFailure()) {
                log.warn("[parseMultiSearchResponse]查询索引失败: {}", item.failure().error());
            }
            results.add(stdlibId);
        });

        return results;
    }

    /**
     * 单个ES查询（保持向后兼容）
     */
    public Long executeEsQuery(Query esQuery) {
        List<Query> queryList = List.of(esQuery);
        List<Long> results = batchExecuteEsQuery(queryList);

        return CollectionUtils.isNotEmpty(results) ? results.getFirst() : null;
    }
}