package com.xyy.saas.inquiry.product.server.service.catalog.processor;

import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import com.xyy.saas.binlog.core.es.EsQueryBuilder;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.product.api.catalog.dto.CatalogExtDTO;
import com.xyy.saas.inquiry.product.api.catalog.dto.rule.MatchRule;
import com.xyy.saas.inquiry.product.api.catalog.dto.rule.MatchRuleOp;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.MedicalCatalogDetailDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 匹配规则处理器
 */
@Slf4j
@Component
public class MatchRuleProcessor {

    /**
     * 根据匹配规则构建ES查询对象
     *
     * @param catalogDetail 医保目录明细
     * @param catalogExt 目录扩展信息
     * @return ES查询对象
     */
    public Query buildEsQuery(MedicalCatalogDetailDO catalogDetail, CatalogExtDTO catalogExt) {
        if (catalogExt == null || catalogExt.getMatchStdlib() != Boolean.TRUE) {
            return null;
        }

        // 根据医疗目录类别选择匹配规则
        List<MatchRule> matchRules = getMatchRulesByProjectType(catalogDetail.getProjectType(), catalogExt);
        if (CollectionUtils.isEmpty(matchRules)) {
            return null;
        }

        List<Query> mustQueries = new ArrayList<>();
        
        for (MatchRule rule : matchRules) {
            try {
                Object targetValue = catalogDetail.getFieldValue(rule.getTargetField());
                if (targetValue == null || StringUtils.isBlank(targetValue.toString())) {
                    continue;
                }

                String sourceField = rule.getSourceField();
                MatchRuleOp op = MatchRuleOp.fromType(rule.getOp());
                if (op == null) {
                    log.warn("[buildEsQuery]未知的匹配操作类型: {}", rule.getOp());
                    continue;
                }

                Query query = buildSingleQuery(sourceField, targetValue.toString(), op);
                if (query != null) {
                    mustQueries.add(query);
                }
            } catch (Exception e) {
                log.error("[buildEsQuery]构建查询条件失败, sourceField: {}, targetField: {}, op: {}",
                    rule.getSourceField(), rule.getTargetField(), rule.getOp(), e);
            }
        }

        if (mustQueries.isEmpty()) {
            return null;
        }

        // 如果只有一个查询条件，直接返回
        if (mustQueries.size() == 1) {
            return mustQueries.getFirst();
        }

        // 多个查询条件使用bool查询组合（AND关系）
        EsQueryBuilder.BoolQueryBuilder boolBuilder = EsQueryBuilder.boolQuery();
        for (Query query : mustQueries) {
            boolBuilder.must(query);
        }
        
        return boolBuilder.build();
    }

    /**
     * 根据医疗目录项目类型获取对应的匹配规则
     */
    private List<MatchRule> getMatchRulesByProjectType(String projectType, CatalogExtDTO catalogExt) {
        MedicineTypeEnum medicineTypeEnum = MedicineTypeEnum.fromProjectType(projectType);
        if (medicineTypeEnum == null) {
            // 默认使用西药匹配规则
            return catalogExt.getWesternMedicineMatchRules();
        }
        
        if (Objects.equals(medicineTypeEnum.getCode(), MedicineTypeEnum.ASIAN_MEDICINE.getCode())) {
            return catalogExt.getWesternMedicineMatchRules();
        } else if (Objects.equals(medicineTypeEnum.getCode(), MedicineTypeEnum.CHINESE_MEDICINE.getCode())) {
            return catalogExt.getChineseMedicineMatchRules();
        } else {
            // 默认使用西药匹配规则
            return catalogExt.getWesternMedicineMatchRules();
        }
    }

    /**
     * 根据匹配操作构建单个ES查询
     *
     * @param sourceField 目标字段
     * @param targetValue 源值
     * @param op 匹配操作
     * @return ES查询对象
     */
    private Query buildSingleQuery(String sourceField, String targetValue, MatchRuleOp op) {
        switch (op) {
            case EQUALS:
                // 精确匹配
                return EsQueryBuilder.termQuery(sourceField, targetValue);
            case HAS:
                // 包含匹配（模糊查询）
                return EsQueryBuilder.wildcardQuery(sourceField, "*" + targetValue + "*");
            case IN:
                // 属于匹配（多值匹配）
                String[] values = targetValue.split("[,，;；|]");
                List<String> valueList = new ArrayList<>();
                for (String value : values) {
                    if (StringUtils.isNotBlank(value)) {
                        valueList.add(value.trim());
                    }
                }
                if (!valueList.isEmpty()) {
                    return EsQueryBuilder.termsQuery(sourceField, valueList);
                }
                break;
            default:
                log.warn("[buildSingleQuery]不支持的匹配操作类型: {}", op.getType());
        }
        return null;
    }
}