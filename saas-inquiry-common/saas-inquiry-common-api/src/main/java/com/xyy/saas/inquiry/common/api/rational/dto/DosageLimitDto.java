package com.xyy.saas.inquiry.common.api.rational.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

/**
 * 用量限制
 *
 * <AUTHOR>
 * @Date 4/18/24 5:36 PM
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DosageLimitDto implements Serializable {

    private static final long serialVersionUID = -394279520691649564L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 标准库ID
     */
    private String standardId;

    /**
     * 通用名
     */
    private String generalName;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 规格
     */
    private String specifications;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 总使用剂量限制
     */
    private Integer totalDoseLimit;

    /**
     * 是否长处方 0否, 1是
     */
    private Integer whetherLongPrescription;

    /**
     * 最小包装数量
     */
    private Integer minimumPackageQuantity;

    /**
     * 告警级别
     */
    private Integer cautionLevel;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String updateUser;

}
