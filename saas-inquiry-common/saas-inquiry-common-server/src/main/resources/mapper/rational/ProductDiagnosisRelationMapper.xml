<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.common.dal.mysql.rational.ProductDiagnosisRelationMapper">

  <select id="selectPages"
    resultType="com.xyy.saas.inquiry.common.controller.admin.rational.vo.ProductDiagnosisRelationRespVO">
    select
    a.id,
    a.diagnosis_code,
    a.product_name,
    a.cnt,
    a.rn,
    a.weight,
    a.yn,
    a.create_time,
    a.update_time,
    a.creator,
    a.updater,
    b.show_name showName,
    b.diagnosis_name diagnosisName
    from inquiry_product_diagnosis_relation a
    left join saas_inquiry_diagnosis b on a.diagnosis_code = b.diagnosis_code
    <where>
      <if test="pageReqVO.yn != null">
        and a.yn = #{pageReqVO.yn}
      </if>
      <if test="pageReqVO.diagnosisCodes != null and pageReqVO.diagnosisCodes.size() > 0 ">
        and a.diagnosis_code in
        <foreach collection="pageReqVO.diagnosisCodes" open="(" close=")" separator="," item="code">
          #{code}
        </foreach>
      </if>
      <if test="pageReqVO.productName != null and pageReqVO.productName != ''">
        and a.product_name like concat('%',#{pageReqVO.productName},'%')
      </if>
      <if test="pageReqVO.diagnosisName != null and pageReqVO.diagnosisName != ''">
        and b.diagnosis_name like concat('%',#{pageReqVO.diagnosisName},'%')
      </if>
      <if test="pageReqVO.showName != null and pageReqVO.showName != ''">
        and b.show_name like concat('%',#{pageReqVO.showName},'%')
      </if>
      <if test="pageReqVO.diagnosisCode != null and pageReqVO.diagnosisCode != ''">
        and a.diagnosis_code = #{pageReqVO.diagnosisCode}
      </if>
    </where>
  </select>


  <select id="pageQueryByProductName" resultType="com.xyy.saas.inquiry.common.controller.admin.rational.vo.ProductDiagnosisRelationRespVO">
    SELECT *
    FROM ykq_product_diagnosis_statistics
    where product_name = trim(#{productName})
      and yn = 0
    order by weight desc, rn
      LIMIT #{offset}, #{pageSize}
  </select>
</mapper>