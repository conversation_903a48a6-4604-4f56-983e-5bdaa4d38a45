<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.common.dal.mysql.rational.InquiryRationalProductMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->
  <resultMap type="com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalProduct" id="InquiryRationalProductMap">
    <result property="id" column="id" jdbcType="INTEGER"/>
    <result property="commonName" column="common_name" jdbcType="VARCHAR"/>
    <result property="categoryLv6Id" column="category_lv6_id" jdbcType="VARCHAR"/>
    <result property="medicareRemark" column="medicare_remark" jdbcType="VARCHAR"/>
    <result property="status" column="status" jdbcType="INTEGER"/>
    <result property="yn" column="yn" jdbcType="INTEGER"/>
    <result property="creator" column="creator" jdbcType="VARCHAR"/>
    <result property="updater" column="updater" jdbcType="VARCHAR"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
  </resultMap>


  <sql id="Base_Column_List">
    id
    , common_name, category_lv6_id, medicare_remark,status, yn, creator, updater, create_time, update_time    </sql>

  <!--查询单个-->
  <select id="queryById" resultMap="InquiryRationalProductMap">
    select
    <include refid="Base_Column_List"/>
    from inquiry_rational_product
    where id = #{id}
  </select>


  <!--查询指定行数据-->
  <select id="queryByCondition" resultMap="InquiryRationalProductMap">
    select
    <include refid="Base_Column_List"/>
    from inquiry_rational_product
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="ids != null and ids.size() > 0 ">
        and id in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
          #{id}
        </foreach>
      </if>
      <if test="commonName != null and commonName != ''">
        and common_name like concat('%',#{commonName},'%')
      </if>
      <if test="commonNames != null and commonNames.size() > 0 ">
        and common_name in
        <foreach collection="commonNames" open="(" close=")" separator="," item="cname">
          #{cname}
        </foreach>
      </if>
      <if test="categoryLv6Id != null and categoryLv6Id != ''">
        and category_lv6_id = #{categoryLv6Id}
      </if>
      <if test="categoryLv6Ids != null and categoryLv6Ids.size() > 0 ">
        and category_lv6_id in
        <foreach collection="categoryLv6Ids" open="(" close=")" separator="," item="cid">
          #{cid}
        </foreach>
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="yn != null">
        and yn = #{yn}
      </if>
      <if test="createUser != null and createUser != ''">
        and creator = #{createUser}
      </if>
      <if test="updateUser != null and updateUser != ''">
        and updater = #{updateUser}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime}
      </if>
      <if test="updateTime != null">
        and update_time = #{updateTime}
      </if>
    </where>
  </select>

  <select id="queryByConditionMaster" resultMap="InquiryRationalProductMap">
    /*FORCE_MASTER*/ select
    <include refid="Base_Column_List"/>
    from inquiry_rational_product
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="ids != null and ids.size() > 0 ">
        and id in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
          #{id}
        </foreach>
      </if>
      <if test="commonName != null and commonName != ''">
        and common_name like concat('%',#{commonName},'%')
      </if>
      <if test="commonNames != null and commonNames.size() > 0 ">
        and common_name in
        <foreach collection="commonNames" open="(" close=")" separator="," item="cname">
          #{cname}
        </foreach>
      </if>
      <if test="categoryLv6Id != null and categoryLv6Id != ''">
        and category_lv6_id = #{categoryLv6Id}
      </if>
      <if test="categoryLv6Ids != null and categoryLv6Ids.size() > 0 ">
        and category_lv6_id in
        <foreach collection="categoryLv6Ids" open="(" close=")" separator="," item="cid">
          #{cid}
        </foreach>
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="yn != null">
        and yn = #{yn}
      </if>
      <if test="createUser != null and createUser != ''">
        and creator = #{createUser}
      </if>
      <if test="updateUser != null and updateUser != ''">
        and updater = #{updateUser}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime}
      </if>
      <if test="updateTime != null">
        and update_time = #{updateTime}
      </if>
    </where>
  </select>

  <!--统计总行数-->
  <select id="count" resultType="java.lang.Long">
    select count(1)
    from inquiry_rational_product
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="commonName != null and commonName != ''">
        and common_name = #{commonName}
      </if>
      <if test="categoryLv6Id != null and categoryLv6Id != ''">
        and category_lv6_id = #{categoryLv6Id}
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="yn != null">
        and yn = #{yn}
      </if>
      <if test="createUser != null and createUser != ''">
        and creator = #{createUser}
      </if>
      <if test="updateUser != null and updateUser != ''">
        and updater = #{updateUser}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime}
      </if>
      <if test="updateTime != null">
        and update_time = #{updateTime}
      </if>
    </where>
  </select>
  <select id="pageQueryByCondition"
    resultType="com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalProduct">
    select
    <include refid="Base_Column_List"/>
    from inquiry_rational_product
    <where>
      <if test="dto.id != null">
        and id = #{dto.id}
      </if>
      <if test="dto.ids != null and dto.ids.size() > 0 ">
        and id in
        <foreach collection="dto.ids" open="(" close=")" separator="," item="id">
          #{id}
        </foreach>
      </if>
      <if test="dto.commonName != null and dto.commonName != ''">
        and common_name like concat('%',#{dto.commonName},'%')
      </if>
      <if test="dto.commonNames != null and dto.commonNames.size() > 0 ">
        and common_name in
        <foreach collection="dto.commonNames" open="(" close=")" separator="," item="cname">
          #{cname}
        </foreach>
      </if>
      <if test="dto.categoryLv6Id != null and dto.categoryLv6Id != ''">
        and category_lv6_id = #{dto.categoryLv6Id}
      </if>
      <if test="dto.categoryLv6Ids != null and dto.categoryLv6Ids.size() > 0 ">
        and category_lv6_id in
        <foreach collection="dto.categoryLv6Ids" open="(" close=")" separator="," item="cid">
          #{cid}
        </foreach>
      </if>
      <if test="dto.status != null">
        and status = #{dto.status}
      </if>
      <if test="dto.yn != null">
        and yn = #{dto.yn}
      </if>
      <if test="dto.createUser != null and dto.createUser != ''">
        and creator = #{dto.createUser}
      </if>
      <if test="dto.updateUser != null and dto.updateUser != ''">
        and updater = #{dto.updateUser}
      </if>
      <if test="dto.createTime != null">
        and create_time = #{dto.createTime}
      </if>
      <if test="dto.updateTime != null">
        and update_time = #{dto.updateTime}
      </if>
    </where>


  </select>

  <update id="updateBatch" parameterType="java.util.List">
    <foreach collection="entities" item="record" separator=";">
      update inquiry_rational_product
      <set>
        <if test="record.commonName != null and record.commonName != ''">
          common_name = #{record.commonName},
        </if>
        <if test="record.categoryLv6Id != null ">
          category_lv6_id = #{record.categoryLv6Id},
        </if>
        <if test="record.medicareRemark != null">
          medicare_remark = #{record.medicareRemark},
        </if>
        <if test="record.status != null">
          status = #{record.status},
        </if>
        <if test="record.yn != null">
          yn = #{record.yn},
        </if>
        <if test="record.createUser != null and record.createUser != ''">
          creator = #{record.createUser},
        </if>
        <if test="record.updateUser != null and record.updateUser != ''">
          updater = #{record.updateUser},
        </if>
        <if test="record.createTime != null">
          create_time = #{record.createTime},
        </if>
        <if test="record.updateTime != null">
          update_time = #{record.updateTime},
        </if>
      </set>
      where id = #{record.id}
    </foreach>
  </update>

  <!--通过主键删除-->
  <delete id="deleteById">
    delete
    from inquiry_rational_product
    where id = #{id}
  </delete>
</mapper>