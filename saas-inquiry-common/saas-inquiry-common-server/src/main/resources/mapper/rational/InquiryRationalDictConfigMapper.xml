<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.common.dal.mysql.rational.InquiryRationalDictConfigMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->
  <resultMap type="com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalDictConfig" id="InquiryRationalDictConfigMap">
    <result property="id" column="id" jdbcType="INTEGER"/>
    <result property="type" column="type" jdbcType="INTEGER"/>
    <result property="name" column="name" jdbcType="VARCHAR"/>
    <result property="value" column="value" jdbcType="VARCHAR"/>
    <result property="description" column="description" jdbcType="VARCHAR"/>
    <result property="status" column="status" jdbcType="INTEGER"/>
    <result property="yn" column="yn" jdbcType="INTEGER"/>
    <result property="sysType" column="sys_type" jdbcType="INTEGER"/>
    <result property="creator" column="creator" jdbcType="VARCHAR"/>
    <result property="updater" column="updater" jdbcType="VARCHAR"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
  </resultMap>


  <sql id="Base_Column_List">
    id
    ,type, name, value, description, status, yn,sys_type, creator, updater, create_time, update_time
  </sql>

  <!--查询单个-->
  <select id="queryById" resultMap="InquiryRationalDictConfigMap">
    select
    <include refid="Base_Column_List"/>
    from inquiry_rational_dict_config
    where id = #{id}
  </select>


  <!--查询指定行数据-->
  <select id="queryByCondition" resultMap="InquiryRationalDictConfigMap">
    select
    <include refid="Base_Column_List"/>
    from inquiry_rational_dict_config
    <include refid="baseCondition"/>
    order by id desc
  </select>


  <select id="pageQueryByCondition"
    resultType="com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalDictConfig">
    select
    <include refid="Base_Column_List"/>
    from inquiry_rational_dict_config
    <include refid="baseCondition"/>
    order by id desc
  </select>


  <sql id="baseCondition">
    <where>
      <if test="dictConfigDto.id != null">
        and id = #{dictConfigDto.id}
      </if>
      <if test="dictConfigDto.ids != null and dictConfigDto.ids.size() > 0 ">
        and id in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
          #{dictConfigDto.id}
        </foreach>
      </if>
      <if test="dictConfigDto.type != null">
        and type = #{dictConfigDto.type}
      </if>
      <if test="dictConfigDto.name != null and dictConfigDto.name != ''">
        and name like concat('%',#{dictConfigDto.name},'%')
      </if>
      <if test="dictConfigDto.names != null and dictConfigDto.names.size() > 0 ">
        and name in
        <foreach collection="dictConfigDto.names" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="dictConfigDto.value != null and dictConfigDto.value != ''">
        and value = #{dictConfigDto.value}
      </if>
      <if test="dictConfigDto.yn != null">
        and yn = #{dictConfigDto.yn}
      </if>
      <if test="dictConfigDto.sysType != null">
        and sys_type = #{dictConfigDto.sysType}
      </if>
      <if test="dictConfigDto.description != null and dictConfigDto.description != ''">
        and description = #{dictConfigDto.description}
      </if>
      <if test="dictConfigDto.status != null">
        and status = #{dictConfigDto.status}
      </if>
    </where>

  </sql>


  <!--统计总行数-->
  <select id="count" resultType="java.lang.Long">
    select count(1)
    from inquiry_rational_dict_config
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="type != null">
        and type = #{type}
      </if>
      <if test="name != null and name != ''">
        and name = #{name}
      </if>
      <if test="value != null and value != ''">
        and value = #{value}
      </if>
      <if test="description != null and description != ''">
        and description = #{description}
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="yn != null">
        and yn = #{yn}
      </if>
      <if test="sysType != null">
        and sys_type = #{sysType}
      </if>
      <if test="createUser != null and createUser != ''">
        and creator = #{createUser}
      </if>
      <if test="updateUser != null and updateUser != ''">
        and updater = #{updateUser}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime}
      </if>
      <if test="updateTime != null">
        and update_time = #{updateTime}
      </if>
    </where>
  </select>


  <update id="updateBatch" parameterType="java.util.List">
    <foreach collection="entities" item="record" separator=";">
      update inquiry_rational_dict_config
      <set>
        <if test="record.type != null">
          type = #{record.type},
        </if>
        <if test="record.name != null and record.name != ''">
          name = #{record.name},
        </if>
        <if test="record.value != null and record.value != ''">
          value = #{record.value},
        </if>
        <if test="record.description != null and record.description != ''">
          description = #{record.description},
        </if>
        <if test="record.status != null">
          status = #{record.status},
        </if>
        <if test="record.yn != null">
          yn = #{record.yn},
        </if>
        <if test="record.sysType != null">
          sys_type = #{record.sysType},
        </if>
        <if test="record.createUser != null and record.createUser != ''">
          creator = #{record.createUser},
        </if>
        <if test="record.updateUser != null and record.updateUser != ''">
          updater = #{record.updateUser},
        </if>
        <if test="record.createTime != null">
          create_time = #{record.createTime},
        </if>
        <if test="record.updateTime != null">
          update_time = #{record.updateTime},
        </if>
      </set>
      where id = #{record.id}
    </foreach>
  </update>

  <!--通过主键删除-->
  <delete id="deleteByIds">
    delete from inquiry_rational_dict_config where id in
    <foreach collection="ids" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>

  </delete>


  <select id="queryDictByTypeNames" resultType="com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalDictConfig">
    select
    <include refid="Base_Column_List"/>
    from inquiry_rational_dict_config
    where yn = 1
    <if test="type != null">
      and type = #{type}
    </if>
    <if test="names != null and names.size() > 0 ">
      and name in
      <foreach collection="names" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
    </if>
  </select>
  <select id="queryRationalDictConfigGroupName"
    resultType="com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalDictConfig">
    select a.* from
    (
    select
    <include refid="Base_Column_List"/>
    from inquiry_rational_dict_config
    where yn = 1
    <if test="id != null">
      and id = #{id}
    </if>
    <if test="type != null">
      and type = #{type}
    </if>
    <if test="name != null and name != ''">
      and name like concat('%',#{name},'%')
    </if>
    <if test="value != null and value != ''">
      and value = #{value}
    </if>
    <if test="description != null and description != ''">
      and description = #{description}
    </if>
    <if test="status != null">
      and status = #{status}
    </if>
    order by id desc
    ) a
    group by a.name
  </select>

</mapper>