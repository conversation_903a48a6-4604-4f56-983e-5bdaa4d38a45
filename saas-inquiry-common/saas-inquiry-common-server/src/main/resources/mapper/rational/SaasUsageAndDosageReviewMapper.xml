<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.common.dal.mysql.rational.SaasUsageAndDosageReviewMapper">
  <update id="updateRelationRuleCount">
    update saas_usage_and_dosage_review
    set relation_rule_count = relation_rule_count + #{count}
    where yn = 1
    and id in
    <foreach collection="idList" index="index" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </update>


  <update id="updateRelationDrugCount">
    update saas_usage_and_dosage_review
    set relation_drug_count = relation_drug_count + #{count}
    where yn = 1
    and id in
    <foreach collection="idList" index="index" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </update>
</mapper>