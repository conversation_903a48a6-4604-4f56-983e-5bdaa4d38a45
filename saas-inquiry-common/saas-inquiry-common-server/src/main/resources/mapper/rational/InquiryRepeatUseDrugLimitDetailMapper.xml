<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.common.dal.mysql.rational.InquiryRepeatUseDrugLimitDetailMapper">

  <select id="queryRepeatUseDrugLimitIdByClassifyIdList" parameterType="java.util.List"  resultType="java.lang.Long">
    select
    distinct repeat_use_drug_limit_id
    from
    inquiry_repeat_use_drug_limit_detail
    where
    yn = 1
    <foreach collection="list" index="index" item="item" open="and classify_id in (" close=")" separator=",">
      #{item}
    </foreach>
  </select>
</mapper>