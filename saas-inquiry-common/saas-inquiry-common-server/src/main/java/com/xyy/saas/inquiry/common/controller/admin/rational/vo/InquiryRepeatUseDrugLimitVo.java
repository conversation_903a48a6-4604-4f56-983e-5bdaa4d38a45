package com.xyy.saas.inquiry.common.controller.admin.rational.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 重复用药限制
 *
 * <AUTHOR>
 * @Date 4/25/24 3:12 PM
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquiryRepeatUseDrugLimitVo implements Serializable {

    private static final long serialVersionUID = 4966344677155366992L;

    /**
     * 主键
     */
    private Long id;
    /**
     * 分类对象
     */
    private List<SaasCategoryVo> saasCategoryVoList;
    /**
     * 状态：0启用，1禁用
     */
    private Integer status;
    /**
     * 有效：0是，1否
     */
    private Byte yn;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建人名称
     */
    private String createUserName;
    /**
     * 修改人
     */
    private String updateUser;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

}
