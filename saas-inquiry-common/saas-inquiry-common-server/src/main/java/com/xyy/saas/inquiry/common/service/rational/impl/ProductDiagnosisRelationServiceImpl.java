package com.xyy.saas.inquiry.common.service.rational.impl;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.common.enums.ErrorCodeConstants.DIAGNOSIS_RELATION_NOT_EXISTS;
import static com.xyy.saas.inquiry.common.enums.ErrorCodeConstants.PRODUCT_DIAGNOSIS_RELATION_EXISTS;
import static com.xyy.saas.inquiry.common.enums.ErrorCodeConstants.PRODUCT_DIAGNOSIS_RELATION_NOT_EXISTS;
import static com.xyy.saas.inquiry.common.enums.ErrorCodeConstants.PRODUCT_DIAGNOSIS_RELATION_NOT_REPEAT;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xyy.saas.inquiry.common.api.rational.dto.ProductDiagnosisRelationSearchDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.ProductDiagnosisRelationPageReqVO;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.ProductDiagnosisRelationRespVO;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.ProductDiagnosisRelationSaveReqVO;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.ProductDiagnosisRelationSaveVO;
import com.xyy.saas.inquiry.common.convert.rational.ProductDiagnosisRelationConvert;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.ProductDiagnosisRelationDO;
import com.xyy.saas.inquiry.common.dal.mysql.rational.ProductDiagnosisRelationMapper;
import com.xyy.saas.inquiry.common.service.rational.ProductDiagnosisRelationService;
import com.xyy.saas.inquiry.hospital.api.diagnosis.InquiryDiagnosisApi;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDto;
import com.xyy.saas.inquiry.pojo.diagnosis.InquiryDiagnosisSearchRespDto;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 商品诊断关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductDiagnosisRelationServiceImpl implements ProductDiagnosisRelationService {

    @Resource
    private ProductDiagnosisRelationMapper productDiagnosisRelationMapper;

    @Resource
    private InquiryDiagnosisApi inquiryDiagnosisApi;

    @Override
    public void createProductDiagnosisRelation(ProductDiagnosisRelationSaveVO saveVO) {

        List<ProductDiagnosisRelationSaveReqVO> itemList = saveVO.getItemList();
        // 参数重复
        List<String> diagnosisCodes = itemList.stream().map(ProductDiagnosisRelationSaveReqVO::getDiagnosisCode).distinct().toList();
        if (CollUtil.size(diagnosisCodes) != itemList.size()) {
            throw exception(PRODUCT_DIAGNOSIS_RELATION_NOT_REPEAT);
        }

        // 校验诊断是否存在
        Map<String, InquiryDiagnosisDto> diagnosisDtoMap = inquiryDiagnosisApi.queryDiagnosisByCondition(InquiryDiagnosisDto.builder().diagnosisCodes(diagnosisCodes).build()).stream()
            .collect(Collectors.toMap(InquiryDiagnosisDto::getDiagnosisCode, Function.identity(), (a, b) -> b));

        String noExistDiagnosisName = itemList.stream().filter(item -> diagnosisDtoMap.get(item.getDiagnosisCode()) == null).map(ProductDiagnosisRelationSaveReqVO::getDiagnosisName).collect(Collectors.joining(","));
        if (StringUtils.isBlank(noExistDiagnosisName)) {
            throw exception(DIAGNOSIS_RELATION_NOT_EXISTS, noExistDiagnosisName);
        }
        // 新增校验商品是否存在
        List<String> productNames = itemList.stream().filter(item -> item.getId() == null).map(ProductDiagnosisRelationSaveReqVO::getProductName).distinct().toList();
        if (CollUtil.isNotEmpty(productNames)) {
            String repeatProductNames = productDiagnosisRelationMapper.selectByCondition(new ProductDiagnosisRelationPageReqVO().setProductNames(productNames)).stream().map(ProductDiagnosisRelationDO::getProductName)
                .collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(repeatProductNames)) {
                throw exception(PRODUCT_DIAGNOSIS_RELATION_EXISTS, repeatProductNames);
            }
        }

        List<ProductDiagnosisRelationDO> list = ProductDiagnosisRelationConvert.INSTANCE.convertSaveDo(itemList);

        productDiagnosisRelationMapper.insertOrUpdate(list);
    }

    @Override
    public void updateProductDiagnosisRelation(ProductDiagnosisRelationSaveReqVO updateReqVO) {
        // 校验存在
        validateProductDiagnosisRelationExists(updateReqVO.getId());
        // 更新
        ProductDiagnosisRelationDO updateObj = BeanUtils.toBean(updateReqVO, ProductDiagnosisRelationDO.class);
        productDiagnosisRelationMapper.updateById(updateObj);
    }

    @Override
    public void deleteProductDiagnosisRelation(Long id) {
        // 校验存在
        validateProductDiagnosisRelationExists(id);
        // 删除
        productDiagnosisRelationMapper.deleteById(id);
    }

    private void validateProductDiagnosisRelationExists(Long id) {
        if (productDiagnosisRelationMapper.selectById(id) == null) {
            throw exception(PRODUCT_DIAGNOSIS_RELATION_NOT_EXISTS);
        }
    }

    @Override
    public ProductDiagnosisRelationRespVO getProductDiagnosisRelation(Long id) {

        ProductDiagnosisRelationDO relationDO = productDiagnosisRelationMapper.selectById(id);

        ProductDiagnosisRelationRespVO relationRespVO = ProductDiagnosisRelationConvert.INSTANCE.convert(relationDO);
        if (relationDO != null) {
            List<InquiryDiagnosisDto> diagnosisDtos = inquiryDiagnosisApi.queryDiagnosisByCondition(InquiryDiagnosisDto.builder().diagnosisCode(relationDO.getDiagnosisCode()).build());
            if (CollUtil.isNotEmpty(diagnosisDtos)) {
                relationRespVO.setDiagnosisName(diagnosisDtos.getFirst().getDiagnosisName());
            }
        }
        return relationRespVO;
    }

    @Override
    public PageResult<ProductDiagnosisRelationRespVO> getProductDiagnosisRelationPage(ProductDiagnosisRelationPageReqVO pageReqVO) {
        IPage<ProductDiagnosisRelationRespVO> pageResult = productDiagnosisRelationMapper.selectPages(MyBatisUtils.buildPage(pageReqVO), pageReqVO);
        if (pageResult == null || CollUtil.isEmpty(pageResult.getRecords())) {
            return PageResult.empty();
        }
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    public List<InquiryDiagnosisSearchRespDto> productDiagnostics(ProductDiagnosisRelationSearchDto searchDto) {
        if (CollUtil.isEmpty(searchDto.getProductNames())) {
            return List.of();
        }
        Map<String, List<ProductDiagnosisRelationRespVO>> map = new HashMap<>();
        for (String productName : searchDto.getProductNames()) {
            map.put(productName, productSearchRecommendDiagnosis(searchDto, productName));
        }
        // 当药品数量超过2个,从第三个药品开始开始诊断只返回一个(前两个商品还是返回三个诊断)
        List<InquiryDiagnosisSearchRespDto> list = new ArrayList<>();
        for (int i = 0; i < searchDto.getProductNames().size(); i++) {
            String productName = searchDto.getProductNames().get(i);
            List<ProductDiagnosisRelationRespVO> diagnosisList = map.get(productName);
            if (CollUtil.isEmpty(diagnosisList)) {
                continue;
            }
            if (i < 2 || diagnosisList.size() == 1) {
                list.addAll(ProductDiagnosisRelationConvert.INSTANCE.convertSearchDtos(diagnosisList));
                continue;
            }
            list.add(ProductDiagnosisRelationConvert.INSTANCE.convertSearchDto(diagnosisList.getFirst()));
        }
        return list;
    }

    private List<ProductDiagnosisRelationRespVO> productSearchRecommendDiagnosis(ProductDiagnosisRelationSearchDto searchDto, String productName) {
        List<ProductDiagnosisRelationRespVO> respVOS = productDiagnosisRelationMapper.pageQueryByProductName(productName, 0, 10);
        // 过滤补全诊断
        if (CollUtil.isEmpty(respVOS)) {
            return List.of();
        }
        List<String> diagnosisCodes = respVOS.stream().map(ProductDiagnosisRelationRespVO::getDiagnosisCode).toList();
        // 校验诊断是否存在 并填充
        Map<String, InquiryDiagnosisDto> diagnosisDtoMap = inquiryDiagnosisApi.queryDiagnosisByCondition(InquiryDiagnosisDto.builder().diagnosisCodes(diagnosisCodes).build()).stream()
            .collect(Collectors.toMap(InquiryDiagnosisDto::getDiagnosisCode, Function.identity(), (a, b) -> b));

        List<ProductDiagnosisRelationRespVO> list = respVOS.stream().filter(item -> diagnosisDtoMap.containsKey(item.getDiagnosisCode()))
            .peek(item -> {
                InquiryDiagnosisDto diagnosisDto = diagnosisDtoMap.get(item.getDiagnosisCode());
                item.setDiagnosisName(diagnosisDto.getDiagnosisName());
                item.setShowName(diagnosisDto.getShowName());
            }).toList();
        // 排序
        return list.stream()
            .sorted(Comparator.comparing(ProductDiagnosisRelationRespVO::getWeight).reversed()
                .thenComparing(ProductDiagnosisRelationRespVO::getRn))
            .skip(searchDto.getRecommendNo())
            .limit(searchDto.getRecommendSize()).toList();
    }
}