package com.xyy.saas.inquiry.common.controller.admin.rational;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.common.api.rational.InquiryRepeatUseDrugLimitApi;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryRepeatUseDrugLimitQueryVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryRepeatUseDrugLimitVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 重复用药限制
 *
 * <AUTHOR>
 * @Date 4/25/24 3:09 PM
 */

@Tag(name = "管理后台 - 合理用药 - 重复用药")
@RestController
@RequestMapping("/rational/repeat/useDrugLimit")
@Validated
public class InquiryRepeatUseDrugLimitController{

    @Resource
    private InquiryRepeatUseDrugLimitApi inquiryRepeatUseDrugLimitApi;

    /**
     * 保存或修改
     *
     * @param inquiryRepeatUseDrugLimitDto
     * @return com.xyy.saas.remote.web.core.utils.ResultVO<java.lang.Boolean>
     * <AUTHOR> 4/25/24 5:49 PM
     */
    @PostMapping(value = "/saveOrUpdate")
    CommonResult<Boolean> saveOrUpdate(@RequestBody InquiryRepeatUseDrugLimitVo inquiryRepeatUseDrugLimitDto) {

        if (CollectionUtils.isEmpty(inquiryRepeatUseDrugLimitDto.getSaasCategoryVoList())) {
            return CommonResult.error("最少添加1个六级分类");
        }

        if (inquiryRepeatUseDrugLimitDto.getSaasCategoryVoList().size() > 20) {
            return CommonResult.error("最多添加20个六级分类");
        }

        return inquiryRepeatUseDrugLimitApi.saveOrUpdate(inquiryRepeatUseDrugLimitDto);
    }

    /**
     * 根据id禁用数据
     *
     * @param inquiryRepeatUseDrugLimitDto
     * @return com.xyy.saas.remote.web.core.utils.ResultVO<java.lang.Boolean>
     * <AUTHOR> 4/25/24 3:27 PM
     */
    @PostMapping(value = "/updateStatusById")
    CommonResult<Boolean> updateStatusById(@RequestBody InquiryRepeatUseDrugLimitVo inquiryRepeatUseDrugLimitDto) {
        return inquiryRepeatUseDrugLimitApi.updateStatusById(inquiryRepeatUseDrugLimitDto);
    }

    /**
     * 分页查询
     *
     * @param inquiryRepeatUseDrugLimitQueryDto
     * @return com.xyy.saas.remote.web.core.utils.ResultVO<com.github.pagehelper.PageInfo < com.xyy.saas.remote.web.core.dto.InquiryRepeatUseDrugLimitVo>>
     * <AUTHOR> 4/26/24 1:55 PM
     */
    @PostMapping(value = "/pageQuery")
    CommonResult<PageResult<InquiryRepeatUseDrugLimitVo>> pageQuery(@RequestBody InquiryRepeatUseDrugLimitQueryVo inquiryRepeatUseDrugLimitQueryDto) {
        return inquiryRepeatUseDrugLimitApi.pageQuery(inquiryRepeatUseDrugLimitQueryDto);
    }

    /**
     * 查询全局配置
     *
     * @param
     * @return com.xyy.saas.remote.web.core.utils.ResultVO<java.lang.Integer>
     * <AUTHOR> 5/6/24 2:16 PM
     */
    @PostMapping(value = "/getGlobalRule")
    CommonResult<Integer> getGlobalRule() {
        return inquiryRepeatUseDrugLimitApi.getGlobalRule();
    }
}
