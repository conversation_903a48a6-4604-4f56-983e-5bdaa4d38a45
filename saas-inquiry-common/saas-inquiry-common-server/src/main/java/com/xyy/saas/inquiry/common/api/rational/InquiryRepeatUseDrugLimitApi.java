package com.xyy.saas.inquiry.common.api.rational;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryRepeatUseDrugLimitQueryVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryRepeatUseDrugLimitVo;


/**
 * 重复用药限制
 *
 * <AUTHOR>
 * @Date 4/25/24 3:07 PM
 */
public interface InquiryRepeatUseDrugLimitApi {

    /**
     * 保存或修改
     *
     * @param inquiryRepeatUseDrugLimitDto
     * @return com.xyy.saas.remote.web.core.utils.ResultVO<java.lang.Boolean>
     * <AUTHOR> 4/25/24 5:49 PM
     */
    CommonResult<Boolean> saveOrUpdate(InquiryRepeatUseDrugLimitVo inquiryRepeatUseDrugLimitDto);

    /**
     * 根据id禁用数据
     *
     * @param inquiryRepeatUseDrugLimitDto
     * @return com.xyy.saas.remote.web.core.utils.ResultVO<java.lang.Boolean>
     * <AUTHOR> 4/25/24 3:27 PM
     */
    CommonResult<Boolean> updateStatusById(InquiryRepeatUseDrugLimitVo inquiryRepeatUseDrugLimitDto);

    /**
     * 分页查询
     *
     * @param inquiryRepeatUseDrugLimitQueryDto
     * @return com.xyy.saas.remote.web.core.utils.ResultVO<com.github.pagehelper.PageInfo<com.xyy.saas.remote.web.core.dto.InquiryRepeatUseDrugLimitVo>>
     * <AUTHOR> 4/26/24 1:55 PM
     */
    CommonResult<PageResult<InquiryRepeatUseDrugLimitVo>> pageQuery(InquiryRepeatUseDrugLimitQueryVo inquiryRepeatUseDrugLimitQueryDto);

    /**
     * 获取全局配置
     *
     * @param
     * @return com.xyy.saas.remote.web.core.utils.ResultVO<java.lang.Integer>
     * <AUTHOR> 5/6/24 2:17 PM
     */
    CommonResult<Integer> getGlobalRule();
}
