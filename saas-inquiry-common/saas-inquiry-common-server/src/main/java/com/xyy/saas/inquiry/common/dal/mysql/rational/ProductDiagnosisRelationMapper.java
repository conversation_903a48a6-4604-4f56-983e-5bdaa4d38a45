package com.xyy.saas.inquiry.common.dal.mysql.rational;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.ProductDiagnosisRelationPageReqVO;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.ProductDiagnosisRelationRespVO;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.ProductDiagnosisRelationDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 商品诊断关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductDiagnosisRelationMapper extends BaseMapperX<ProductDiagnosisRelationDO> {

    default PageResult<ProductDiagnosisRelationDO> selectPage(ProductDiagnosisRelationPageReqVO reqVO) {
        return selectPage(reqVO, getQueryWrapper(reqVO));
    }

    IPage<ProductDiagnosisRelationRespVO> selectPages(Page<ProductDiagnosisRelationPageReqVO> objectPage, ProductDiagnosisRelationPageReqVO pageReqVO);

    default List<ProductDiagnosisRelationDO> selectByCondition(ProductDiagnosisRelationPageReqVO reqVO) {
        return selectList(getQueryWrapper(reqVO));
    }

    private static LambdaQueryWrapperX<ProductDiagnosisRelationDO> getQueryWrapper(ProductDiagnosisRelationPageReqVO reqVO) {
        return new LambdaQueryWrapperX<ProductDiagnosisRelationDO>()
            .eqIfPresent(ProductDiagnosisRelationDO::getDiagnosisCode, reqVO.getDiagnosisCode())
            .likeIfPresent(ProductDiagnosisRelationDO::getProductName, reqVO.getProductName())
            .inIfPresent(ProductDiagnosisRelationDO::getProductName, reqVO.getProductNames())
            .inIfPresent(ProductDiagnosisRelationDO::getDiagnosisCode, reqVO.getDiagnosisCodes())
            .eqIfPresent(ProductDiagnosisRelationDO::getCnt, reqVO.getCnt())
            .eqIfPresent(ProductDiagnosisRelationDO::getRn, reqVO.getRn())
            .eqIfPresent(ProductDiagnosisRelationDO::getWeight, reqVO.getWeight())
            .eqIfPresent(ProductDiagnosisRelationDO::getYn, reqVO.getYn())
            .betweenIfPresent(ProductDiagnosisRelationDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(ProductDiagnosisRelationDO::getId);
    }

    List<ProductDiagnosisRelationRespVO> pageQueryByProductName(@Param("productName") String productName, @Param("offset") int offset, @Param("pageSize") int pageSize);
}