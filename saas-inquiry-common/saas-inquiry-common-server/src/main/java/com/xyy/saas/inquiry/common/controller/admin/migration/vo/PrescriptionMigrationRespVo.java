package com.xyy.saas.inquiry.common.controller.admin.migration.vo;

import com.xyy.saas.inquiry.annotation.InquiryDateType;
import com.xyy.saas.inquiry.pojo.migration.PrescriptionMigrationInfoDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2019/09/11
 * @Describe
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PrescriptionMigrationRespVo extends PrescriptionMigrationInfoDto {

    /**
     * 开方时间
     */
    @InquiryDateType("outPrescriptionTime")
    private String outPrescriptionTimeStr;

    /**
     * 上传时间
     */
    @InquiryDateType("createTime")
    private String createTimeStr;

    /**
     * 审核时间
     */
    @InquiryDateType("approvalTime")
    private String approvalTimeStr;

    /**
     * 接诊时间
     */
    @InquiryDateType("startTime")
    private String startTimeStr;
    /**
     * 结束时间
     */
    @InquiryDateType("endTime")
    private String endTimeStr;

}