package com.xyy.saas.inquiry.common.controller.admin.migration;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationExportVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationImInfoRespVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationInfoRespVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationQueryVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationRespVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationStoreExportVo;
import com.xyy.saas.inquiry.common.enums.migration.SourceAndClientTypeEnum;
import com.xyy.saas.inquiry.common.enums.migration.ThirdPlatformClientTypeEnum;
import com.xyy.saas.inquiry.common.service.migration.MigrationPrescriptionService;
import com.xyy.saas.inquiry.constant.ValidateGroup.Add;
import com.xyy.saas.inquiry.constant.ValidateGroup.Query;
import com.xyy.saas.inquiry.constant.ValidateGroup.Update;
import com.xyy.saas.inquiry.pojo.migration.MigrationPrescriptionReqDto;
import com.xyy.saas.inquiry.pojo.migration.PrescriptionMigrationInfoDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "后台 - 历史处方")
@RestController
@RequestMapping("/common/history-prescription")
@Validated
public class MigrationPrescriptionController {

    @Resource
    private MigrationPrescriptionService migrationPrescriptionService;

    @PostMapping("/migration")
    public CommonResult<Boolean> migrationPrescription(@Valid @RequestBody MigrationPrescriptionReqDto reqDto) {
        migrationPrescriptionService.migrationPrescription(reqDto);
        return success(true);
    }

    @PostMapping("/page-system")
    @Operation(summary = "系统 查询历史处方 / 审方记录")
    public CommonResult<PageResult<PrescriptionMigrationInfoDto>> pageQueryMigrationPrescription(@Validated(value = {Update.class}) @RequestBody PrescriptionMigrationQueryVo reqDto) {
        handleQueryVo(reqDto);
        PageResult<PrescriptionMigrationInfoDto> pageResult = migrationPrescriptionService.pageQueryMigrationPrescription(reqDto);
        return success(pageResult);
    }


    @PostMapping("/export-system")
    @Operation(summary = "系统-导出历史处方")
    public void exportSystemQueryMigrationPrescription(@Validated(value = {Update.class}) @RequestBody PrescriptionMigrationQueryVo reqDto,
        HttpServletResponse response) throws IOException {

        handleQueryVo(reqDto);

        List<PrescriptionMigrationExportVo> list = migrationPrescriptionService.exportSystemQueryMigrationPrescription(reqDto);

        ExcelUtils.write(response, "处方记录.xls", "数据", PrescriptionMigrationExportVo.class,
            list);
    }


    @PostMapping("/page-store")
    @Operation(summary = "门店查询历史处方")
    public CommonResult<PageResult<PrescriptionMigrationRespVo>> pageStoreQueryMigrationPrescription(@Validated(value = {Update.class}) @RequestBody PrescriptionMigrationQueryVo reqDto) {

        PageResult<PrescriptionMigrationRespVo> pageResult = migrationPrescriptionService.pageStoreQueryMigrationPrescription(reqDto);

        return success(pageResult);
    }

    @PostMapping("/get")
    @Operation(summary = "获取历史处方-详情")
    public CommonResult<PrescriptionMigrationInfoRespVo> queryMigrationPrescription(@Validated(value = {Update.class, Query.class}) @RequestBody PrescriptionMigrationQueryVo reqDto) {
        return success(migrationPrescriptionService.queryMigrationPrescription(reqDto));
    }

    @PostMapping("/get-im-list")
    @Operation(summary = "获取历史处方-聊天列表详情")
    public CommonResult<List<PrescriptionMigrationImInfoRespVo>> queryMigrationPrescriptionImList(@Validated(value = {Update.class, Query.class}) @RequestBody PrescriptionMigrationQueryVo reqDto) {
        return success(migrationPrescriptionService.queryMigrationPrescriptionImList(reqDto));
    }

    @PostMapping("/export-store")
    @Operation(summary = "门店-导出历史处方")
    public void exportStoreQueryMigrationPrescription(@Validated(value = {Update.class, Add.class}) @RequestBody PrescriptionMigrationQueryVo reqDto,
        HttpServletResponse response) throws IOException {

        List<PrescriptionMigrationStoreExportVo> list = migrationPrescriptionService.exportStoreQueryMigrationPrescription(reqDto);

        ExcelUtils.write(response, "处方记录.xls", "数据", PrescriptionMigrationStoreExportVo.class,
            list);
    }


    /**
     * 处理业务查询参数
     *
     * @param queryVo
     */
    private void handleQueryVo(PrescriptionMigrationQueryVo queryVo) {
        if (queryVo.getSourceAndClientType() != null) {
            Integer sourceAndClientType = queryVo.getSourceAndClientType();
            SourceAndClientTypeEnum enumByValue = SourceAndClientTypeEnum.getEnumByValue(sourceAndClientType);
            if (enumByValue != null) {
                queryVo.setSource(enumByValue.getSourceValue());
                queryVo.setClientType(enumByValue.getClientTypeValue());
            } else if (ThirdPlatformClientTypeEnum.getByCode(sourceAndClientType) != null) {
                queryVo.setSource(ThirdPlatformClientTypeEnum.getByCode(sourceAndClientType).getSource());
                queryVo.setClientType(ThirdPlatformClientTypeEnum.getByCode(sourceAndClientType).getClientType());
            }
        }
        // 废弃处方查询参数转化
        if (Objects.equals(queryVo.getSource(), 13)) {
            queryVo.setStatus(null);
            queryVo.setAuditType(99);
        }
    }

}