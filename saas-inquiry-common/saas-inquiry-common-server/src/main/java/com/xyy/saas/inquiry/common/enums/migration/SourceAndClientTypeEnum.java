package com.xyy.saas.inquiry.common.enums.migration;

import static com.xyy.saas.inquiry.common.enums.migration.ClientTypeEnum.APP;
import static com.xyy.saas.inquiry.common.enums.migration.ClientTypeEnum.H5;
import static com.xyy.saas.inquiry.common.enums.migration.PrescriptionSourceEnum.BX;
import static com.xyy.saas.inquiry.common.enums.migration.PrescriptionSourceEnum.HSY;
import static com.xyy.saas.inquiry.common.enums.migration.PrescriptionSourceEnum.HY_MD;
import static com.xyy.saas.inquiry.common.enums.migration.PrescriptionSourceEnum.LGB;
import static com.xyy.saas.inquiry.common.enums.migration.PrescriptionSourceEnum.LVDI;
import static com.xyy.saas.inquiry.common.enums.migration.PrescriptionSourceEnum.LZINQUIRY;
import static com.xyy.saas.inquiry.common.enums.migration.PrescriptionSourceEnum.NINHEALTH;
import static com.xyy.saas.inquiry.common.enums.migration.PrescriptionSourceEnum.SCANCODEINQUIRY;
import static com.xyy.saas.inquiry.common.enums.migration.PrescriptionSourceEnum.SMARTFACE;
import static com.xyy.saas.inquiry.common.enums.migration.PrescriptionSourceEnum.SUNSHINE;
import static com.xyy.saas.inquiry.common.enums.migration.PrescriptionSourceEnum.THIRDPARTY;
import static com.xyy.saas.inquiry.common.enums.migration.PrescriptionSourceEnum.TK;
import static com.xyy.saas.inquiry.common.enums.migration.PrescriptionSourceEnum.YDH;
import static com.xyy.saas.inquiry.common.enums.migration.PrescriptionSourceEnum.YUANMENG;
import static com.xyy.saas.inquiry.common.enums.migration.PrescriptionSourceEnum.YZH;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Getter;

@Getter
public enum SourceAndClientTypeEnum {
    LZ_APP(0, APP.getValue(), LZINQUIRY.getValue(), "荷叶健康商家版"),
    LZ_H5(1, H5.getValue(), LZINQUIRY.getValue(), "荷叶健康pc"),
    YKQ(2, H5.getValue(), PrescriptionSourceEnum.YKQ.getValue(), "宜块钱"),
    SMARTFACE_WEB(3, H5.getValue(), SMARTFACE.getValue(), "智慧脸管理端"),
    SCANCODE(4, H5.getValue(), SCANCODEINQUIRY.getValue(), "线下扫码补方"),
    NIN_HEALTH(5, APP.getValue(), NINHEALTH.getValue(), "您健康"),
    BX_H5(6, H5.getValue(), BX.getValue(), "保险"),
    YZH_h5(7, H5.getValue(), YZH.getValue(), "荷叶通"),
    LVDI_h5(8, H5.getValue(), LVDI.getValue(), "荷叶健康小程序"),
    HY_h5(9, H5.getValue(), HY_MD.getValue(), "荷叶门店"),
    LGB_h5(10, H5.getValue(), LGB.getValue(), "老干部"),
    YDH_h5(11, H5.getValue(), YDH.getValue(), "医带患"),
    TK_h5(12, H5.getValue(), TK.getValue(), "泰康"),
    SUNSHINE_h5(13, H5.getValue(), SUNSHINE.getValue(), "阳光"),
    YUANMENG_h5(14, H5.getValue(), YUANMENG.getValue(), "远盟"),
    //    HYDEE_APP(15, APP.getValue(), HYDEE.getValue(),"海典商家版"),
//    HYDEE_PC(16, H5.getValue(), HYDEE.getValue(),"海典PC"),
    THIRD_PARTY_PLATFORM_APP(15, APP.getValue(), THIRDPARTY.getValue(), "三方ERP 商家版"),
    THIRD_PARTY_PLATFORM_PC(16, H5.getValue(), THIRDPARTY.getValue(), "三方ERP PC"),
    HYS(17, H5.getValue(), HSY.getValue(), "互医师"),
    ;
    Integer value;
    Integer clientTypeValue;
    Integer sourceValue;
    String descript;

    SourceAndClientTypeEnum(Integer value, Integer clientTypeValue, Integer sourceValue, String descript) {
        this.value = value;
        this.clientTypeValue = clientTypeValue;
        this.sourceValue = sourceValue;
        this.descript = descript;
    }

    private static Map<Integer, SourceAndClientTypeEnum> enumMap = Collections.unmodifiableMap(initEnumMap());

    private static Map<Integer, SourceAndClientTypeEnum> initEnumMap() {
        return Arrays.asList(values()).stream().collect(Collectors.toMap(SourceAndClientTypeEnum::getValue, ele -> ele));
    }

    public static SourceAndClientTypeEnum getEnumByValue(Integer value) {
        return enumMap.get(value);
    }

    public static SourceAndClientTypeEnum getEnumByClientTypeAndSource(Integer clientTypeValue, Integer sourceValue) {

        if (clientTypeValue == null || sourceValue == null) {
            return null;
        }
        for (SourceAndClientTypeEnum element : enumMap.values()) {
            if (element.getClientTypeValue().equals(clientTypeValue) && element.getSourceValue().equals(sourceValue)) {
                return element;
            }
        }
        return null;
    }
}
