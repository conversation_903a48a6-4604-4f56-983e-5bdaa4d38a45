package com.xyy.saas.inquiry.common.service.migration.impl;

import com.xyy.saas.inquiry.common.dal.dataobject.migration.MigrationPrescriptionRecord;
import com.xyy.saas.inquiry.common.dal.mysql.migration.MigrationPrescriptionRecordMapper;
import com.xyy.saas.inquiry.common.service.migration.MigrationPrescriptionFailRecordService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 处方迁移失败记录服务实现类
 *
 * @Author:chenxiaoyi
 * @Date:2025/08/20 21:37
 */
@Slf4j
@Service
public class MigrationPrescriptionFailRecordServiceImpl implements MigrationPrescriptionFailRecordService {

    @Resource
    private MigrationPrescriptionRecordMapper failRecordMapper;

    @Override
    public void saveRecord(MigrationPrescriptionRecord failRecord) {
        if (failRecord.getCreateTime() == null) {
            failRecord.setCreateTime(LocalDateTime.now());
        }
        if (failRecord.getUpdateTime() == null) {
            failRecord.setUpdateTime(LocalDateTime.now());
        }
        failRecordMapper.insert(failRecord);
    }


    @Override
    public void createEsSuccessRecord(String organSign, Long minId, Long maxId, Integer count) {
        MigrationPrescriptionRecord record = new MigrationPrescriptionRecord()
            .setOrganSign(organSign)
            .setMinId(minId)
            .setMaxId(maxId)
            .setCount(count)
            .setStatus(0); // 迁移成功

        saveRecord(record);
    }

    @Override
    public void createMqFailRecord(String organSign, Long minId, Long maxId, String failReason) {
        MigrationPrescriptionRecord failRecord = new MigrationPrescriptionRecord()
            .setOrganSign(organSign)
            .setMinId(minId)
            .setMaxId(maxId)
            .setFailReason(StringUtils.substring(failReason, 0, 1000))
            .setStatus(1); // MQ发送失败

        saveRecord(failRecord);
    }

    @Override
    public void createEsFailRecord(String organSign, Long minId, Long maxId, String failReason) {
        MigrationPrescriptionRecord failRecord = new MigrationPrescriptionRecord()
            .setOrganSign(organSign)
            .setMinId(minId)
            .setMaxId(maxId)
            .setFailReason(StringUtils.substring(failReason, 0, 1000))
            .setStatus(2); // ES存储失败

        saveRecord(failRecord);
    }
}
