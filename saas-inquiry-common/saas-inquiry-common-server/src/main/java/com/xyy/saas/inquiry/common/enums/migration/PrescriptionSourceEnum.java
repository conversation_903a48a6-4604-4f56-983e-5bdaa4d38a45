package com.xyy.saas.inquiry.common.enums.migration;

import lombok.Getter;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 0灵芝问诊，1宜块钱 2 智慧脸  3  扫码问诊 4 您健康
 */
@Getter
public enum PrescriptionSourceEnum {
    LZINQUIRY(0, "荷叶问诊"), YKQ(1, "宜块钱"), SMARTFACE(2, "智慧脸"), SCANCODEINQUIRY(3, "扫码问诊"),
    NINHEALTH(4, "您健康"), BX(5, "保险"), YZH(6, "荷叶通"), LVDI(7, "荷叶健康小程序"), HY_<PERSON>(8, "荷叶门店"),
    LGB(9, "老干部"), YDH(10, "医带患"), TK(11, "泰康"), SUNSHINE(12, "阳光"), YUANMENG(13, "远盟"), THIRDPARTY(14, "三方ERP"),
    HSY(17, "互医师");


    Integer value;
    String descript;

    PrescriptionSourceEnum(Integer value, String descript) {
        this.value = value;
        this.descript = descript;
    }

    /**
     * 处方类型：上传处方
     */
    private final static byte UPLOAD_PRESCRIPTION = 3;
    private final static String UPLOAD_PRESCRIPTION_DESCRIPTION = "上传处方";

    private final static byte HYT = 6;
    private final static String HYT_DESCRIPTION = "灵芝问诊";

    private static Map<Integer, PrescriptionSourceEnum> prescriptionSourceEnumMap = Collections.unmodifiableMap(initPrescriptionSourceEnumMap());

    private static Map<Integer, PrescriptionSourceEnum> initPrescriptionSourceEnumMap() {
        return Arrays.asList(values()).stream().collect(Collectors.toMap(PrescriptionSourceEnum::getValue, ele -> ele));
    }

    public static boolean validValue(Integer value) {
        return value != null && prescriptionSourceEnumMap.containsKey(value);
    }

    public static String getSourceInfo(Integer value, Byte type) {

        if (type != null && UPLOAD_PRESCRIPTION == type) {
            return UPLOAD_PRESCRIPTION_DESCRIPTION;
        } else if (type != null && HYT == value) {
            return HYT_DESCRIPTION;
        }

        PrescriptionSourceEnum prescriptionSourceEnum = prescriptionSourceEnumMap.get(new Integer(value));
        return prescriptionSourceEnum == null ? null : prescriptionSourceEnum.getDescript();
    }
}
