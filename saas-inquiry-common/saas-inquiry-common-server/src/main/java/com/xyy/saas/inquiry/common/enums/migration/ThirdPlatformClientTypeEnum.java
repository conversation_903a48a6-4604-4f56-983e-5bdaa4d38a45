package com.xyy.saas.inquiry.common.enums.migration;

import lombok.Getter;

/**
 * 40 app, 70 h5
 */
@Getter
public enum ThirdPlatformClientTypeEnum {

    hydee(81, "海典", 81, null),// IOS端服务包专用，与问诊客户端无关
    cox(82, "舵手", 82, null),
    inca(83, "英克", 83, null),
    cqzffk(84, "字符飞快", 84, null),
    yu<PERSON><PERSON>(85, "雨人", 85, null),
    yao<PERSON><PERSON><PERSON><PERSON>(86, "药聚汇", 86, null),

    hydee40(8140, "海典APP", 81, 40),// IOS端服务包专用，与问诊客户端无关
    hydee70(8170, "海典PC", 81, 70),// IOS端服务包专用，与问诊客户端无关
    cox40(8240, "舵手APP", 82, 40),
    cox70(8270, "舵手PC", 82, 70),
    inca40(8340, "英克APP", 83, 40),
    inca70(8370, "英克PC", 83, 70),
    cqzffk40(8440, "字符飞快APP", 84, 40),
    cqzffk70(8470, "字符飞快PC", 84, 70),
    yuRen40(8540, "雨人APP", 85, 40),
    yuRen70(8570, "雨人PC", 85, 70),
    yaoJuHui40(8640, "药聚汇APP", 86, 40),
    yaoJuHui70(8670, "药聚汇PC", 86, 70),

    ;
    private Integer code;

    private String message;

    private Integer source;

    private Integer clientType;

    ThirdPlatformClientTypeEnum(Integer code, String message, Integer source, Integer clientType) {
        this.code = code;
        this.message = message;
        this.source = source;
        this.clientType = clientType;
    }

    public static ThirdPlatformClientTypeEnum getByCode(Integer code) {
        for (ThirdPlatformClientTypeEnum value : ThirdPlatformClientTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
