package com.xyy.saas.inquiry.common.constant;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2024/12/12 13:55
 */
public class MigrationConstant {

    /**
     * 处方迁移每批大小
     */
    public static final String MIGRATION_PRESCRIPTION_PAGE_SIZE = "migration.prescription.page_size";
    /**
     * 处方迁移 延迟时间 单位:毫秒
     */
    public static final String MIGRATION_PRESCRIPTION_DELAY_TIME = "migration.prescription.delay.time";
    /**
     * 处方迁移开关
     */
    public static final String MIGRATION_PRESCRIPTION_SWITCH = "migration.prescription.switch";

    // ES 索引相关
    /**
     * 历史处方ES索引前缀
     */
    public static final String HISTORY_PRESCRIPTION_INDEX_PREFIX = "inquiry_history_prescription_";

    // Redis 键名常量
    /**
     * 处方迁移任务标识键
     */
    public static final String MIGRATION_PRESCRIPTION_FLAG_KEY = "migration:prescription:flag";

    /**
     * 处方迁移ES进度键前缀
     */
    public static final String MIGRATION_PRESCRIPTION_ES_PROGRESS_KEY = "migration:prescription:es:progress:";

}
