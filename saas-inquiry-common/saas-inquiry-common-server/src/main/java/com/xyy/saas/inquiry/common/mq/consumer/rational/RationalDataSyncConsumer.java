package com.xyy.saas.inquiry.common.mq.consumer.rational;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.util.spring.SpringUtils;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.common.handler.rational.sync.RationalDataServiceEnum;
import com.xyy.saas.inquiry.common.handler.rational.sync.RationalDataSyncEsBaseService;
import com.xyy.saas.inquiry.common.mq.message.rational.RationalDataSyncEvent;
import com.xyy.saas.inquiry.common.mq.message.rational.dto.RationalData2EsMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/26 11:03
 * @Description: 医生调度消费者
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_common_mq_consumer_rational_RationalDataSyncConsumer",
    topic = RationalDataSyncEvent.TOPIC)
public class RationalDataSyncConsumer {

    @EventBusListener
    public void rationalDataSyncEs(RationalDataSyncEvent event) {
        RationalData2EsMsg dto = event.getMsg();
        // 为空直接return
        if(ObjectUtil.isEmpty(dto)){
            log.error("===>RationalData2EsConsumer 获取消息数据为空");
            return;
        }
        RationalDataServiceEnum dataServiceEnum = RationalDataServiceEnum.findByTable(dto.getTableName());
        if (dataServiceEnum == null) {
            log.error("===>RationalData2EsConsumer 消息数据异常 msgId:{}，tableType:{}", event.getMsgId(), dto.getType());
            return;
        }
        RationalDataSyncEsBaseService esBaseService = SpringUtils.getBean(dataServiceEnum.getService());

        switch (dto.getType()) {
            case "INSERT":
                esBaseService.insert(dto);
                break;
            case "UPDATE":
                esBaseService.update(dto);
                break;
            case "DELETE":
                esBaseService.delete(dto);
                break;
            default:
                break;
        }
    }
}
