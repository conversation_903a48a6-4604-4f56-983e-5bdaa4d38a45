package com.xyy.saas.inquiry.common.controller.admin.migration.vo;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.xyy.saas.inquiry.annotation.InquiryDateType;
import com.xyy.saas.inquiry.pojo.migration.PrescriptionMigrationDetailDto;
import com.xyy.saas.inquiry.pojo.migration.PrescriptionMigrationInfoDto;
import com.xyy.saas.inquiry.pojo.migration.PrescriptionMigrationTcmDto;
import java.util.Collections;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2019/09/11
 * @Describe
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class PrescriptionMigrationInfoRespVo extends PrescriptionMigrationInfoDto {

    /**
     * 开方时间
     */
    @InquiryDateType("outPrescriptionTime")
    private String outPrescriptionTimeStr;

    /**
     * 上传时间
     */
    @InquiryDateType("createTime")
    private String createTimeStr;

    /**
     * 审核时间
     */
    @InquiryDateType("approvalTime")
    private String approvalTimeStr;

    /**
     * 接诊时间
     */
    @InquiryDateType("startTime")
    private String startTimeStr;
    /**
     * 结束时间
     */
    @InquiryDateType("endTime")
    private String endTimeStr;

    // ==================== 转换方法 ====================

    /**
     * 获取处方明细列表（从JSON字符串转换）
     *
     * @return 处方明细列表
     */
    public List<PrescriptionMigrationDetailDto> getPrescriptionDetailsList() {
        if (StrUtil.isBlank(getPrescriptionDetails())) {
            return Collections.emptyList();
        }
        try {
            return JSONUtil.toList(getPrescriptionDetails(), PrescriptionMigrationDetailDto.class);
        } catch (Exception e) {
            log.warn("处方明细JSON解析失败，原始数据：{}", getPrescriptionDetails(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取复诊图片列表（从JSON字符串转换）
     *
     * @return 图片URL列表
     */
    public List<String> getCheckImageUrlList() {
        if (StrUtil.isBlank(getCheckImageList())) {
            return Collections.emptyList();
        }
        try {
            return JSONUtil.toList(getCheckImageList(), String.class);
        } catch (Exception e) {
            log.warn("复诊图片列表JSON解析失败，原始数据：{}", getCheckImageList(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取中药用法用量
     */
    public PrescriptionMigrationTcmDto getDosageAndUsageDto() {
        if (StrUtil.isBlank(getDosageAndUsage())) {
            return new PrescriptionMigrationTcmDto();
        }
        try {
            return JSONUtil.toBean(getDosageAndUsage(), PrescriptionMigrationTcmDto.class);
        } catch (Exception e) {
            return new PrescriptionMigrationTcmDto();
        }
    }

}