package com.xyy.saas.inquiry.common.api.rational.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.common.api.rational.InquiryRepeatUseDrugLimitApi;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryRepeatUseDrugLimitQueryVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryRepeatUseDrugLimitVo;
import com.xyy.saas.inquiry.common.service.rational.InquiryRepeatUseDrugLimitService;
import com.xyy.saas.inquiry.drugstore.api.option.InquiryOptionConfigApi;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigQueryDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import java.util.HashSet;

/**
 * 重复用药限制表(InquiryRepeatUseDrugLimit)表控制层
 *
 * <AUTHOR>
 * @since 2024-04-25 14:40:23
 */
@Slf4j
@Service
public class InquiryRepeatUseDrugLimitImpl implements InquiryRepeatUseDrugLimitApi {

    @Resource
    private InquiryRepeatUseDrugLimitService inquiryRepeatUseDrugLimitService;

    @DubboReference
    private InquiryOptionConfigApi inquiryOptionConfigApi;


    @Override
    public CommonResult<Boolean> saveOrUpdate(InquiryRepeatUseDrugLimitVo inquiryRepeatUseDrugLimitDto) {

        if (inquiryRepeatUseDrugLimitDto == null || CollectionUtils.isEmpty(inquiryRepeatUseDrugLimitDto.getSaasCategoryVoList())) {
            return CommonResult.error("参数缺失");
        }

        return CommonResult.success(inquiryRepeatUseDrugLimitService.saveOrUpdate(inquiryRepeatUseDrugLimitDto));
    }

    @Override
    public CommonResult<Boolean> updateStatusById(InquiryRepeatUseDrugLimitVo inquiryRepeatUseDrugLimitDto) {

        return CommonResult.success(inquiryRepeatUseDrugLimitService.updateStatusById(inquiryRepeatUseDrugLimitDto));
    }

    @Override
    public CommonResult<PageResult<InquiryRepeatUseDrugLimitVo>> pageQuery(InquiryRepeatUseDrugLimitQueryVo inquiryRepeatUseDrugLimitQueryDto) {

        return CommonResult.success(inquiryRepeatUseDrugLimitService.pageQuery(inquiryRepeatUseDrugLimitQueryDto));
    }

    @Override
    public CommonResult<Integer> getGlobalRule() {
        InquiryOptionGlobalConfigRespDto globalConfig = inquiryOptionConfigApi.getInquiryOptionGlobalConfig(new InquiryOptionConfigQueryDto());
        if (globalConfig == null || ObjectUtil.notEqual(globalConfig.getProcRepeatDrugSwitch(),Boolean.TRUE)) {
            return CommonResult.success(CommonStatusEnum.DISABLE.getStatus());
        }

        return CommonResult.success(CommonStatusEnum.ENABLE.getStatus());
    }
}

