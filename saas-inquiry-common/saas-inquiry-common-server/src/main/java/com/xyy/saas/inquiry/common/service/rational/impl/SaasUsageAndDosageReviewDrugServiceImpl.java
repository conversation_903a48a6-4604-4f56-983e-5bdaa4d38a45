package com.xyy.saas.inquiry.common.service.rational.impl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.SaasUsageAndDosageReviewDrugDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.SaasUsageAndDosageReviewDrugQueryDto;
import com.xyy.saas.inquiry.common.convert.rational.RationalConvert;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.SaasUsageAndDosageReviewDrugPo;
import com.xyy.saas.inquiry.common.dal.mysql.rational.SaasUsageAndDosageReviewDrugMapper;
import com.xyy.saas.inquiry.common.service.rational.SaasUsageAndDosageReviewDrugService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用法用量药品
 *
 * <AUTHOR>
 * @Date 9/10/24 5:22 PM
 */
@Slf4j
@Service
public class SaasUsageAndDosageReviewDrugServiceImpl implements SaasUsageAndDosageReviewDrugService {

    @Resource
    private SaasUsageAndDosageReviewDrugMapper saasUsageAndDosageReviewDrugMapper;

    @Override
    public void batchInsert(List<SaasUsageAndDosageReviewDrugPo> saasUsageAndDosageReviewDrugPoList) {

        if (CollectionUtils.isEmpty(saasUsageAndDosageReviewDrugPoList)) {
            return;
        }

        saasUsageAndDosageReviewDrugMapper.insertBatch(saasUsageAndDosageReviewDrugPoList);
    }

    @Override
    public List<SaasUsageAndDosageReviewDrugPo> queryByCondition(SaasUsageAndDosageReviewDrugQueryDto saasUsageAndDosageReviewDrugQueryDto) {

        List<SaasUsageAndDosageReviewDrugPo> saasUsageAndDosageReviewDrugPoList = saasUsageAndDosageReviewDrugMapper.queryByCondition(saasUsageAndDosageReviewDrugQueryDto);

        return CollectionUtils.isEmpty(saasUsageAndDosageReviewDrugPoList) ? Lists.newArrayList() : saasUsageAndDosageReviewDrugPoList;
    }

    @Override
    public PageResult<SaasUsageAndDosageReviewDrugDto> pageQueryByCondition(SaasUsageAndDosageReviewDrugQueryDto saasUsageAndDosageReviewDrugQueryDto) {
        PageResult<SaasUsageAndDosageReviewDrugPo> poPageInfo = saasUsageAndDosageReviewDrugMapper.pageQueryByCondition(saasUsageAndDosageReviewDrugQueryDto);

        return RationalConvert.INSTANCE.convertUsageAndDosageReviewDrugPage(poPageInfo);
    }

    @Override
    public void deleteById(SaasUsageAndDosageReviewDrugDto saasUsageAndDosageReviewDrugDto) {

        if (CollectionUtils.isEmpty(saasUsageAndDosageReviewDrugDto.getIdList())
                && (saasUsageAndDosageReviewDrugDto.getUsageAndDosageReviewId() == null || saasUsageAndDosageReviewDrugDto.getUsageAndDosageReviewId() <= 0)) {
            log.info("SaasUsageAndDosageReviewDrugService#deleteById saasUsageAndDosageReviewDrugDto:{}", saasUsageAndDosageReviewDrugDto);
            return;
        }

        saasUsageAndDosageReviewDrugMapper.deleteById(saasUsageAndDosageReviewDrugDto);
    }
}
