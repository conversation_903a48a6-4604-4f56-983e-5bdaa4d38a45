package com.xyy.saas.inquiry.common.api.rational.impl;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.common.api.rational.InquiryCompatibilitySearchApi;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryCompatibilityDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryCompatibilitySearchParam;
import com.xyy.saas.inquiry.common.util.SwitchUtil;
import com.xyy.saas.inquiry.enums.rational.RemoteIndexEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.Criteria;
import org.springframework.data.elasticsearch.core.query.CriteriaQuery;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchAggregations;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregation;
import co.elastic.clients.elasticsearch._types.aggregations.TermsAggregation;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsAggregate;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsBucket;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class InquiryCompatibilitySearchImpl implements InquiryCompatibilitySearchApi<InquiryCompatibilityDto> {

    @Resource
    private ElasticsearchOperations elasticsearchOperations;
    @Override
    public Boolean indexCreate() {
        try {
            IndexCoordinates indexCoordinates = IndexCoordinates.of(RemoteIndexEnum.YKQ_REMOTE_COMPATIBILITY.getIndexName());
            IndexOperations indexOperations = elasticsearchOperations.indexOps(indexCoordinates);
            
            // 检查索引是否已存在
            if (indexOperations.exists()) {
                log.info("索引已存在: {}", RemoteIndexEnum.YKQ_REMOTE_COMPATIBILITY.getIndexName());
                return true;
            }
            
            // 创建索引
            boolean created = indexOperations.create();
            log.info("索引创建结果: {}", created);
            return created;
        } catch (Exception e) {
            log.error("创建索引失败", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<String, Object> getMapping() {
        try {
            IndexCoordinates indexCoordinates = IndexCoordinates.of(RemoteIndexEnum.YKQ_REMOTE_COMPATIBILITY.getIndexName());
            IndexOperations indexOperations = elasticsearchOperations.indexOps(indexCoordinates);
            return indexOperations.getMapping();
        } catch (Exception e) {
            log.error("获取映射失败", e);
            return null;
        }
    }

    @Override
    public Boolean indexDelete() {
        try {
            IndexCoordinates indexCoordinates = IndexCoordinates.of(RemoteIndexEnum.YKQ_REMOTE_COMPATIBILITY.getIndexName());
            IndexOperations indexOperations = elasticsearchOperations.indexOps(indexCoordinates);
            boolean deleted = indexOperations.delete();
            log.info("索引删除结果: {}", deleted);
            return deleted;
        } catch (Exception e) {
            log.error("删除索引失败", e);
            return false;
        }
    }

    @Override
    public Boolean indexExists() {
        try {
            IndexCoordinates indexCoordinates = IndexCoordinates.of(RemoteIndexEnum.YKQ_REMOTE_COMPATIBILITY.getIndexName());
            IndexOperations indexOperations = elasticsearchOperations.indexOps(indexCoordinates);
            return indexOperations.exists();
        } catch (Exception e) {
            log.error("检查索引是否存在失败", e);
            return false;
        }
    }

    @Override
    public boolean add(InquiryCompatibilityDto compatibilityDto) {
        log.info("保存配伍禁忌的参数：{}", JSON.toJSONString(compatibilityDto));
        try {
            IndexCoordinates indexCoordinates = IndexCoordinates.of(RemoteIndexEnum.YKQ_REMOTE_COMPATIBILITY.getIndexName());
            InquiryCompatibilityDto saved = elasticsearchOperations.save(compatibilityDto, indexCoordinates);
            boolean success = saved != null;
            log.info("保存ES的结果：{}", success);
            return success;
        } catch (Exception e) {
            log.error("保存文档失败", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean update(InquiryCompatibilityDto compatibilityDto) {
        log.info("修改配伍禁忌的参数：{}", JSON.toJSONString(compatibilityDto));
        try {
            IndexCoordinates indexCoordinates = IndexCoordinates.of(RemoteIndexEnum.YKQ_REMOTE_COMPATIBILITY.getIndexName());
            InquiryCompatibilityDto updated = elasticsearchOperations.save(compatibilityDto, indexCoordinates);
            boolean success = updated != null;
            log.info("修改ES的结果：{}", success);
            return success;
        } catch (Exception e) {
            log.error("更新文档失败", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean batchDel(List<Integer> ids) {
        log.info("删除配伍禁忌配置的参数：{}", JSON.toJSONString(ids));
        try {
            IndexCoordinates indexCoordinates = IndexCoordinates.of(RemoteIndexEnum.YKQ_REMOTE_COMPATIBILITY.getIndexName());
            for (Integer id : ids) {
                elasticsearchOperations.delete(id.toString(), indexCoordinates);
            }
            log.info("删除数据的结果：成功");
            return true;
        } catch (Exception e) {
            log.error("批量删除文档失败", e);
            return false;
        }
    }

    @Override
    public boolean batchInsert(List<InquiryCompatibilityDto> dtoList) {
        log.info("批量新增配伍禁忌的参数：{}", JSON.toJSONString(dtoList));
        try {
            IndexCoordinates indexCoordinates = IndexCoordinates.of(RemoteIndexEnum.YKQ_REMOTE_COMPATIBILITY.getIndexName());
            Iterable<InquiryCompatibilityDto> saved = elasticsearchOperations.save(dtoList, indexCoordinates);
            boolean success = saved != null;
            log.info("批量新增的结果：{}", success);
            return success;
        } catch (Exception e) {
            log.error("批量插入文档失败", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public CommonResult<PageResult<InquiryCompatibilityDto>> pageSearchInquiryCompatibility(InquiryCompatibilitySearchParam param) {
        //添加默认分页信息
        if(param.getPageNo()<1 || param.getPageSize()<1){
            param.setPageNo(1);
            param.setPageSize(10000);
        }
        try {
            // 构建查询条件
            Criteria criteria = buildSearchCriteria(param);
            
            // 构建分页和排序
            Pageable pageable = PageRequest.of(param.getPageNo() - 1, param.getPageSize(), Sort.by(Sort.Direction.DESC, "_id"));
            
            // 构建查询
            Query query = new CriteriaQuery(criteria).setPageable(pageable);
            
            // 执行查询
            IndexCoordinates indexCoordinates = IndexCoordinates.of(RemoteIndexEnum.YKQ_REMOTE_COMPATIBILITY.getIndexName());
            SearchHits<InquiryCompatibilityDto> searchHits = elasticsearchOperations.search(query, InquiryCompatibilityDto.class, indexCoordinates);
            
            if(searchHits.isEmpty()){
                return CommonResult.success(new PageResult<>());
            }
            
            List<InquiryCompatibilityDto> compatibilitys = new ArrayList<>();
            searchHits.forEach(hit -> compatibilitys.add(hit.getContent()));
            
            PageResult page = new PageResult();
            page.setTotal(searchHits.getTotalHits());
            page.setList(compatibilitys);
            return CommonResult.success(page);
        } catch (Exception e) {
            log.error("分页查询失败", e);
            throw new RuntimeException(e);
        }
    }



    @Override
    public CommonResult<PageResult<InquiryCompatibilityDto>> webPageSearchInquiryCompatibilityAgg(InquiryCompatibilitySearchParam searchParam) {
        //添加默认分页信息
        if(searchParam.getPageNo()<1 || searchParam.getPageSize()<1){
            searchParam.setPageNo(1);
            searchParam.setPageSize(10000);
        }
        
        try {
            // 构建查询条件
            Criteria criteria = buildSearchCriteria(searchParam);
            
            // 构建聚合查询
            var searchQuery = NativeQuery.builder()
                    .withQuery(new CriteriaQuery(criteria))
                    .withAggregation("compatAgg", Aggregation.of(a -> a
                            .terms(TermsAggregation.of(t -> t
                                    .field("compatId")
                                    .size(10000)
                            ))
                    ))
                    .withMaxResults(0) // 不返回文档，只返回聚合结果
                    .build();
            
            IndexCoordinates index = IndexCoordinates.of(RemoteIndexEnum.YKQ_REMOTE_COMPATIBILITY.getIndexName());
            SearchHits<Map> searchHits = elasticsearchOperations.search(searchQuery, Map.class, index);
            
            List<InquiryCompatibilityDto> dtos = Lists.newArrayList();
            
            // 处理聚合结果
            if (searchHits.hasAggregations()) {
                ElasticsearchAggregations aggregations = (ElasticsearchAggregations) searchHits.getAggregations();
                if (aggregations != null) {
                    // 获取terms聚合结果
                    var compatAgg = aggregations.aggregationsAsMap().get("compatAgg");
                    if (compatAgg != null && compatAgg.aggregation().getAggregate().isSterms()) {
                        StringTermsAggregate termsAggregate = compatAgg.aggregation().getAggregate().sterms();
                        var buckets = termsAggregate.buckets().array();
                        
                        if(CollectionUtils.isNotEmpty(buckets)){
                            int endSet = getOffsetEnd(searchParam, buckets.size());
                            int offset = (searchParam.getPageNo() - 1) * searchParam.getPageSize();
                            for(int i=offset; i < endSet; i++){
                                StringTermsBucket bucket = buckets.get(i);
                                InquiryCompatibilityDto dto = new InquiryCompatibilityDto();
                                dto.setCompatId(Integer.valueOf(bucket.key().stringValue()));
                                dtos.add(dto);
                            }
                        }
                        
                        PageResult page = new PageResult();
                        page.setTotal((long) buckets.size());
                        page.setList(dtos);
                        return CommonResult.success(page);
                    }
                }
            }
            
            // 如果没有聚合结果，返回空结果
            PageResult page = new PageResult();
            page.setTotal(0L);
            page.setList(dtos);
            return CommonResult.success(page);
            
        } catch (Exception e) {
            log.error("聚合查询失败: {}", e.getMessage(), e);
            throw new RuntimeException("聚合查询失败", e);
        }
    }

    private int getOffsetEnd(InquiryCompatibilitySearchParam param, int totalSize) {
        int pageNum = param.getPageNo();
        int pageSize = param.getPageSize();
        if(totalSize < pageSize){
            return totalSize;
        }
        int end = pageNum * pageSize;
        return Math.min(totalSize, end);
    }


    private Criteria buildSearchCriteria(InquiryCompatibilitySearchParam param) {
        Criteria criteria = new Criteria();
        
        if(param.getCaution() != null){
            criteria = criteria.and("caution").is(param.getCaution());
        }
        
        if(StringUtils.isNotBlank(param.getPref())){
            criteria = criteria.and("compatPref").is(param.getPref());
        }
        
        if(param.getStatus() != null){
            criteria = criteria.and("status").is(param.getStatus());
        }
        
        // 单独查一次满足的类别
        if(CollectionUtils.isNotEmpty(param.getCategorieNames())){
            criteria = criteria.and("firstType").is(1)
                    .and("firstParam").in(param.getCategorieNames());
        }
        
        // terms should 关系
        if(Objects.equals(param.getSearchType(),1)){
            Criteria shouldCriteria = new Criteria();
            
            if(CollectionUtils.isNotEmpty(param.getCommonNames())){
                Criteria commonNamesCriteria = new Criteria("firstType").is(2)
                        .and("firstParam").in(param.getCommonNames());
                shouldCriteria = shouldCriteria.or(commonNamesCriteria);
            }
            
            // 名称模糊
            if(StringUtils.isNotBlank(param.getCommonName())){
                Criteria commonNameCriteria = new Criteria("firstType").is(2)
                        .and("firstParam").is(param.getCommonName());
                shouldCriteria = shouldCriteria.or(commonNameCriteria);
            }
            
            // 名称关联下的分类下 should
            if(CollectionUtils.isNotEmpty(param.getCategorys())){
                Criteria categorysCriteria = new Criteria("firstType").is(1)
                        .and("firstParam").in(param.getCategorys());
                shouldCriteria = shouldCriteria.or(categorysCriteria);
            }
            
            if(shouldCriteria != null){
                criteria = criteria.and(shouldCriteria);
            }
        }else{
            Criteria shouldCriteria = new Criteria();
            
            // like should 名称模糊
            if(StringUtils.isNotBlank(param.getCommonName())){
                String wildcardName = "*" + SwitchUtil.replaceEsWildLikeStr(param.getCommonName()) + "*";
                Criteria wildcardCriteria = new Criteria("commonProductsFirst").matches(wildcardName)
                        .or(new Criteria("commonProductsSecond").matches(wildcardName));
                shouldCriteria = shouldCriteria.or(wildcardCriteria);
            }
            
            if(CollectionUtils.isNotEmpty(param.getCategorys())){
                Criteria categorysCriteria = new Criteria("firstType").is(1)
                        .and("firstParam").in(param.getCategorys());
                shouldCriteria = shouldCriteria.or(categorysCriteria);
            }
            
            if(shouldCriteria != null){
                criteria = criteria.and(shouldCriteria);
            }
        }
        
        return criteria;
    }


}
