###  1.门店登录  请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "DF12sa#FD"
}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log(response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
%}


### 2.诊断禁忌列表查询
POST {{baseAdminKernelUrl}}/kernel/rational/diagnosis/queryList
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{
  "diagnosisPref": "",
  "diagnosisName": "",
  "categoryName": "",
  "commonName": "",
  "caution": null,
  "status": null,
  "pageNo": 1,
  "pageSize": 50
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}


### 3.配伍禁忌列表查询
POST {{baseAdminKernelUrl}}/kernel/rational/compatibility/queryList
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{
  "pref": "",
  "categoryName": "",
  "commonName": "",
  "caution": null,
  "status": null,
  "pageNum": 1,
  "pageSize": 50
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}

### 4.限制类药品列表查询
POST {{baseAdminKernelUrl}}/kernel/rational/product/queryList
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{
  "pageNo": 1,
  "pageSize": 50,
  "commonName": "",
  "categoryLv6Id": "",
  "ageLimits": null,
  "ageRangeLimit": null,
  "healthLimit": null,
  "womenLimit": null,
  "age": "0",
  "day1": 0,
  "day2": "天",
  "day3": 1,
  "day4": "岁",
  "ingredientList": [],
  "diagnosisList": [],
  "compatibilityCommonList": []
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}

### 5.限制类药品列表查询
POST {{baseAdminKernelUrl}}/kernel/usageAndDosage/review/deleteById
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{
  "id": 1958107168524861442
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}

### 6.用法用量审查规则查询
POST {{baseAdminKernelUrl}}/kernel/usageAndDosage/review/rule/queryByCondition
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{"usageAndDosageReviewId":"1958401213681442817"}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}

### 7.诊断禁忌查询
POST {{baseAdminKernelUrl}}/kernel/rational/diagnosis/queryList
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{"categoryName":"","caution":null,"commonName":"","diagnosisName":"","diagnosisPref":"","pageNo":1,"pageSize":50,"status":null}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}

### 8.配伍禁忌查询
POST {{baseAdminKernelUrl}}/kernel/rational/compatibility/queryList
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{"categoryName":"","caution":null,"commonName":"","pageNo":1,"pageSize":50,"pref":"","status":null}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  console.log(response.body)
%}

### 9.限制类药品列表查询
POST {{baseAdminKernelUrl}}/kernel/dosageLimit/pageQuery
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{
  "pageNum": 1,
  "pageSize": 50,
  "generalName": "",
  "standardId": "",
  "whetherLongPrescription": ""
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  console.log(response.body)
%}

### 10.查询关联药品
POST {{baseAdminKernelUrl}}/kernel/usageAndDosage/review/drug/pageSearchCenterDrug
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{"commonName":"感冒灵","specification":"10g","pageNo":1,"pageSize":10}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  console.log(response.body)
%}

### 11.新增诊断禁忌
POST {{baseAdminKernelUrl}}/kernel/rational/diagnosis/createOrUpdate
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{"diagnosisPref":"","caution":2,"status":0,"description":"测试3","diagnosisCodes":["异物入目"],"categories":[15156],"commonProducts":["634325321131"]}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  console.log(response.body)
%}

