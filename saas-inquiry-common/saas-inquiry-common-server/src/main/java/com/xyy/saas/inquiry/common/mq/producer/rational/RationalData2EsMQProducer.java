package com.xyy.saas.inquiry.common.mq.producer.rational;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.common.mq.message.rational.RationalDataSyncEvent;
import com.xyy.saas.inquiry.common.mq.message.rational.dto.RationalData2EsMsg;
import com.xyy.saas.inquiry.util.RedisUtils;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @DateTime: 2025/8/7 14:19
 * @Description: 合理用药数据同步消息生产者
 **/
@Component
@EventBusProducer(
    topic = RationalDataSyncEvent.TOPIC
)
@Slf4j
public class RationalData2EsMQProducer extends EventBusRocketMQTemplate {

    private RationalData2EsMQProducer getSelf() {
        return SpringUtil.getBean(getClass());
    }

    /**
     * 异步
     */
    public Boolean rationalData2EsAsync(RationalData2EsMsg esMsg,Boolean... lock) {
        log.info("#into#method:{}#param{}", "rationalData2Es--->inquiryPrescriptionPoList={}", JSON.toJSONString(esMsg));
        try {
            // 校验重复不在投递  锁10min
            if(lock.length>0){
                if (!RedisUtils.tryGetDistributedLock("rationalMqOnceKey:"+esMsg.hashCode(),Thread.currentThread().getName(),10*60)) {
                    return false;
                }
            }
            getSelf().sendOrderedMessage(RationalDataSyncEvent.builder().msg(esMsg).build(), LocalDateTime.now().plusSeconds(5));
        } catch (Exception e) {
            log.error("rationalData2Es发送消息出现异常exception=", e);
            return false;
        }
        log.info("rationalData2Es发送异步消息完成inquiryPrescriptionPoList={}",JSON.toJSONString(esMsg));
        return true;
    }

}
