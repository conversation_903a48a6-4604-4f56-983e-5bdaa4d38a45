package com.xyy.saas.inquiry.common.controller.admin.rational;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.ProductDiagnosisRelationPageReqVO;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.ProductDiagnosisRelationRespVO;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.ProductDiagnosisRelationSaveReqVO;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.ProductDiagnosisRelationSaveVO;
import com.xyy.saas.inquiry.common.service.rational.ProductDiagnosisRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 商品诊断关联")
@RestController
@RequestMapping("/inquiry/product-diagnosis-relation")
@Validated
public class ProductDiagnosisRelationController {

    @Resource
    private ProductDiagnosisRelationService productDiagnosisRelationService;

    @PostMapping("/save")
    @Operation(summary = "创建商品诊断关联")
    // @PreAuthorize("@ss.hasPermission('inquiry:product-diagnosis-relation:create')")
    public CommonResult<Boolean> createProductDiagnosisRelation(@Valid @RequestBody ProductDiagnosisRelationSaveVO saveVO) {
        productDiagnosisRelationService.createProductDiagnosisRelation(saveVO);
        return success(true);
    }

    @PutMapping("/update")
    @Operation(summary = "更新商品诊断关联")
    // @PreAuthorize("@ss.hasPermission('inquiry:product-diagnosis-relation:update')")
    public CommonResult<Boolean> updateProductDiagnosisRelation(@Valid @RequestBody ProductDiagnosisRelationSaveReqVO updateReqVO) {
        productDiagnosisRelationService.updateProductDiagnosisRelation(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品诊断关联")
    @Parameter(name = "id", description = "编号", required = true)
    // @PreAuthorize("@ss.hasPermission('inquiry:product-diagnosis-relation:delete')")
    public CommonResult<Boolean> deleteProductDiagnosisRelation(@RequestParam("id") Long id) {
        productDiagnosisRelationService.deleteProductDiagnosisRelation(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品诊断关联")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    // @PreAuthorize("@ss.hasPermission('inquiry:product-diagnosis-relation:query')")
    public CommonResult<ProductDiagnosisRelationRespVO> getProductDiagnosisRelation(@RequestParam("id") Long id) {
        ProductDiagnosisRelationRespVO productDiagnosisRelation = productDiagnosisRelationService.getProductDiagnosisRelation(id);
        return success(productDiagnosisRelation);
    }

    @GetMapping("/page")
    @Operation(summary = "获得商品诊断关联分页")
    @PreAuthorize("@ss.hasPermission('inquiry:product-diagnosis-relation:query')")
    public CommonResult<PageResult<ProductDiagnosisRelationRespVO>> getProductDiagnosisRelationPage(@Valid ProductDiagnosisRelationPageReqVO pageReqVO) {
        PageResult<ProductDiagnosisRelationRespVO> pageResult = productDiagnosisRelationService.getProductDiagnosisRelationPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商品诊断关联 Excel")
    @PreAuthorize("@ss.hasPermission('inquiry:product-diagnosis-relation:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportProductDiagnosisRelationExcel(@Valid ProductDiagnosisRelationPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProductDiagnosisRelationRespVO> list = productDiagnosisRelationService.getProductDiagnosisRelationPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "商品诊断关联.xls", "数据", ProductDiagnosisRelationRespVO.class, list);
    }

}