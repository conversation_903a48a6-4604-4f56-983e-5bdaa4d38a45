package com.xyy.saas.inquiry.common.dal.mysql.rational;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.LimitDrugCatalogOperationVo;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryDrugCatalogLimitPo;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRepeatUseDrugLimitPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * @Author: xucao
 * @DateTime: 2025/8/18 18:51
 * @Description: 问诊限制类用药mapper
 **/
@Mapper
public interface InquiryDrugCatalogLimitMapper extends BaseMapperX<InquiryDrugCatalogLimitPo> {

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<InquiryDrugCatalogLimit> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertByDuplicateKey(@Param("entities") List<InquiryDrugCatalogLimitPo> entities);

    /**
     * 查询指定行数据
     *
     * @param operationVo 查询条件
     * @return 对象列表
     */
     default PageResult<InquiryDrugCatalogLimitPo> queryByCondition(LimitDrugCatalogOperationVo operationVo){
        return selectPage(operationVo,new LambdaQueryWrapperX<InquiryDrugCatalogLimitPo>()
            .eqIfPresent(InquiryDrugCatalogLimitPo::getId,operationVo.getId())
            .eqIfPresent(InquiryDrugCatalogLimitPo::getProvince,operationVo.getProvinceCode())
            .eqIfPresent(InquiryDrugCatalogLimitPo::getCity,operationVo.getCityCode())
            .eqIfPresent(InquiryDrugCatalogLimitPo::getArea,operationVo.getAreaCode())
            .eqIfPresent(InquiryDrugCatalogLimitPo::getHospitalPref,operationVo.getHospitalPref())
            .inIfPresent(InquiryDrugCatalogLimitPo::getCatalogId,operationVo.getCatalogIdList())
            .eqIfPresent(InquiryDrugCatalogLimitPo::getLimitType,operationVo.getLimitType())
            .orderByDesc(InquiryDrugCatalogLimitPo::getId)
        );
     }

     default List<InquiryDrugCatalogLimitPo> queryList(LimitDrugCatalogOperationVo operationVo) {
        return selectList(new LambdaQueryWrapperX<InquiryDrugCatalogLimitPo>()
            .eqIfPresent(InquiryDrugCatalogLimitPo::getId,operationVo.getId())
            .eqIfPresent(InquiryDrugCatalogLimitPo::getProvince,operationVo.getProvinceCode())
            .eqIfPresent(InquiryDrugCatalogLimitPo::getCity,operationVo.getCityCode())
            .eqIfPresent(InquiryDrugCatalogLimitPo::getArea,operationVo.getAreaCode())
            .eqIfPresent(InquiryDrugCatalogLimitPo::getHospitalPref,operationVo.getHospitalPref())
            .inIfPresent(InquiryDrugCatalogLimitPo::getCatalogId,operationVo.getCatalogIdList())
            .eqIfPresent(InquiryDrugCatalogLimitPo::getLimitType,operationVo.getLimitType())
            .orderByDesc(InquiryDrugCatalogLimitPo::getId)
        );
     }

    /**
     * 通过主键删除数据
     *
     * @param idList 主键
     * @return 影响行数
     */
    int physicalDeleteByIdList(@Param("idList") List<Long> idList);
}
