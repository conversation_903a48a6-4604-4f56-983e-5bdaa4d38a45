package com.xyy.saas.inquiry.common.enums.migration;

import lombok.Getter;

/**
 * STATUS_11 : 4-10等(排除1、2、3)枚举集合 对应为已开方 此枚举只用于查询时的查询条件
 */
@Getter
public enum PerscriptionStatusEnum {

    CREATE(1, "待开方"),
    CLOSED(2, "已关闭"),
    OUTTIME(3, "已超时"),
    SUBMIT(4, "开方成功、等待上传"),
    UPLOAD(5, "上传成功"),
    SIGN(6, "待签名"),
    SIGNOVER(7, "医师签名成功"),

    STATUS_8(8, "医师签名失败"),
    STATUS_9(9, "药师签名成功"),
    STATUS_10(10, "药师签名失败"),
    STATUS_11(11, "已开方"),
    STATUS_12(12, "医师撤回"),

    ;


    private Integer code;

    private String message;

    PerscriptionStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static String getMsg(Integer code) {
        for (PerscriptionStatusEnum perscriptionStatusEnum : PerscriptionStatusEnum.values()) {
            if (perscriptionStatusEnum.getCode().equals(code)) {
                return perscriptionStatusEnum.getMessage();
            }
        }
        return null;
    }
}
