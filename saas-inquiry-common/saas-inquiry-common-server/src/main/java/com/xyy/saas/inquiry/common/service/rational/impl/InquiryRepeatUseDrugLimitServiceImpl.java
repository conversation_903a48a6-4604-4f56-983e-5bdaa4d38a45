package com.xyy.saas.inquiry.common.service.rational.impl;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryRepeatUseDrugLimitQueryVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryRepeatUseDrugLimitVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.SaasCategoryVo;
import com.xyy.saas.inquiry.common.convert.rational.RationalConvert;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRepeatUseDrugLimitDetailPo;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRepeatUseDrugLimitPo;
import com.xyy.saas.inquiry.common.dal.mysql.rational.InquiryRepeatUseDrugLimitDetailMapper;
import com.xyy.saas.inquiry.common.dal.mysql.rational.InquiryRepeatUseDrugLimitMapper;
import com.xyy.saas.inquiry.common.service.productmid.InquiryMidCategoryApiImpl;
import com.xyy.saas.inquiry.common.service.rational.InquiryRepeatUseDrugLimitService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/**
 * 重复用药限制表(InquiryRepeatUseDrugLimit)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-04-25 14:40:39
 */
@Slf4j
@Service
public class InquiryRepeatUseDrugLimitServiceImpl implements InquiryRepeatUseDrugLimitService {

    @Resource
    private InquiryRepeatUseDrugLimitMapper inquiryRepeatUseDrugLimitMapper;

    @Resource
    private InquiryRepeatUseDrugLimitDetailMapper inquiryRepeatUseDrugLimitDetailMapper;

    @Resource
    private InquiryMidCategoryApiImpl inquiryMidCategoryApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdate(InquiryRepeatUseDrugLimitVo inquiryRepeatUseDrugLimitDto) {

        Long repeatUseDrugLimitId = inquiryRepeatUseDrugLimitDto.getId();

        // 修改
        if (inquiryRepeatUseDrugLimitDto.getId() != null && inquiryRepeatUseDrugLimitDto.getId() > 0) {

            InquiryRepeatUseDrugLimitPo inquiryRepeatUseDrugLimitPo = InquiryRepeatUseDrugLimitPo.builder()
                    .id(inquiryRepeatUseDrugLimitDto.getId()).build();

            inquiryRepeatUseDrugLimitMapper.updateById(inquiryRepeatUseDrugLimitPo);
            // 删除明细数据
            inquiryRepeatUseDrugLimitDetailMapper.delete(InquiryRepeatUseDrugLimitDetailPo::getRepeatUseDrugLimitId,inquiryRepeatUseDrugLimitDto.getId());
        } else {
            InquiryRepeatUseDrugLimitPo inquiryRepeatUseDrugLimitPo = InquiryRepeatUseDrugLimitPo.builder()
                    .status(CommonStatusEnum.ENABLE.getStatus())
                    .yn(CommonStatusEnum.ENABLE.getStatus().byteValue()).build();
            inquiryRepeatUseDrugLimitMapper.insert(inquiryRepeatUseDrugLimitPo);

            repeatUseDrugLimitId = inquiryRepeatUseDrugLimitPo.getId();
        }

        // 统一新增明细数据
        if (repeatUseDrugLimitId != null && repeatUseDrugLimitId > 0) {

            final Long finalRepeatUseDrugLimitId = repeatUseDrugLimitId;
            List<InquiryRepeatUseDrugLimitDetailPo> inquiryRepeatUseDrugLimitDetailPo = inquiryRepeatUseDrugLimitDto.getSaasCategoryVoList().stream()
                    .map(item -> InquiryRepeatUseDrugLimitDetailPo.builder()
                            .repeatUseDrugLimitId(finalRepeatUseDrugLimitId)
                            .classifyId(item.getId())
                            .yn(CommonStatusEnum.ENABLE.getStatus().byteValue()).build()).collect(Collectors.toList());

            inquiryRepeatUseDrugLimitDetailMapper.insertBatch(inquiryRepeatUseDrugLimitDetailPo);
        }

        return true;
    }

    @Override
    public Boolean updateStatusById(InquiryRepeatUseDrugLimitVo inquiryRepeatUseDrugLimitDto) {

        if (inquiryRepeatUseDrugLimitDto == null || inquiryRepeatUseDrugLimitDto.getId() == null || inquiryRepeatUseDrugLimitDto.getId() <= 0
                || (!CommonStatusEnum.ENABLE.getStatus().equals(inquiryRepeatUseDrugLimitDto.getStatus()) && !CommonStatusEnum.ENABLE.getStatus().equals(inquiryRepeatUseDrugLimitDto.getStatus()))) {
            return false;
        }

        InquiryRepeatUseDrugLimitPo updateData = InquiryRepeatUseDrugLimitPo.builder()
                .id(inquiryRepeatUseDrugLimitDto.getId())
                .status(inquiryRepeatUseDrugLimitDto.getStatus()).build();

        inquiryRepeatUseDrugLimitMapper.updateById(updateData);

        return true;
    }

    @Override
    public PageResult<InquiryRepeatUseDrugLimitVo> pageQuery(InquiryRepeatUseDrugLimitQueryVo inquiryRepeatUseDrugLimitQueryDto) {

        if (inquiryRepeatUseDrugLimitQueryDto.getClassifyId() != null && inquiryRepeatUseDrugLimitQueryDto.getClassifyId() > 0) {

            Map<Integer, List<SaasCategoryVo>> integerListMap = inquiryMidCategoryApi.queryCategoryChildPathByIds(Lists.newArrayList(inquiryRepeatUseDrugLimitQueryDto.getClassifyId()));
            log.info("pageQuery queryCategoryChildPathByIds:{}", JSON.toJSONString(integerListMap));

            if (MapUtils.isEmpty(integerListMap)) {
                return new PageResult<>();
            }

            List<Integer> classifyIdList = integerListMap.values().stream().findFirst().orElse(Lists.newArrayList()).stream().filter(item -> item.getId() != null && item.getId() > 0).map(SaasCategoryVo::getId).distinct().collect(toList());

            if (CollectionUtils.isEmpty(classifyIdList)) {
                return new PageResult<>();
            }

            List<Long> repeatUseDrugLimitIdList = inquiryRepeatUseDrugLimitDetailMapper.queryRepeatUseDrugLimitIdByClassifyIdList(classifyIdList);

            if (CollectionUtils.isEmpty(repeatUseDrugLimitIdList)) {
                return new PageResult<>();
            }

            inquiryRepeatUseDrugLimitQueryDto.setIdList(repeatUseDrugLimitIdList);
        }


        PageResult<InquiryRepeatUseDrugLimitPo> pageResult = inquiryRepeatUseDrugLimitMapper.selectPage(inquiryRepeatUseDrugLimitQueryDto);
        PageResult<InquiryRepeatUseDrugLimitVo> inquiryRepeatUseDrugLimitDtoPageInfo = RationalConvert.INSTANCE.convertPageInfo(pageResult);
        List<InquiryRepeatUseDrugLimitVo> inquiryRepeatUseDrugLimitDtoPageInfoList = inquiryRepeatUseDrugLimitDtoPageInfo.getList();

        if (CollectionUtils.isNotEmpty(inquiryRepeatUseDrugLimitDtoPageInfoList)) {

            // 获取创建人id集合
            List<Integer> createUserIdList = inquiryRepeatUseDrugLimitDtoPageInfoList.stream().filter(item -> StringUtils.isNotBlank(item.getCreateUser())).map(item -> {
                try {
                    return Integer.parseInt(item.getCreateUser());
                } catch (Exception e) {
                    return null;
                }
            }).filter(Objects::nonNull).collect(toList());


            // 查询 类别对应得分类详情表
            List<InquiryRepeatUseDrugLimitDetailPo> inquiryRepeatUseDrugLimitDetailPoList =
                inquiryRepeatUseDrugLimitDetailMapper.queryList(InquiryRepeatUseDrugLimitQueryVo.builder().idList(inquiryRepeatUseDrugLimitDtoPageInfoList.stream().map(InquiryRepeatUseDrugLimitVo::getId).collect(toList())).build());
            // 分组(K - 重复用药主表id ，V - 六级分类末级id集合)
            Map<Long, List<Integer>> inquiryRepeatUseDrugLimitDetailPoListMap = inquiryRepeatUseDrugLimitDetailPoList.stream().collect(Collectors.groupingBy(InquiryRepeatUseDrugLimitDetailPo::getRepeatUseDrugLimitId,
                    Collectors.mapping(InquiryRepeatUseDrugLimitDetailPo::getClassifyId, Collectors.toList())));

            // 查询中台六级分类上游链路
            List<Integer> classifyIds = inquiryRepeatUseDrugLimitDetailPoList.stream().map(InquiryRepeatUseDrugLimitDetailPo::getClassifyId).collect(toList());
            Map<Integer, List<SaasCategoryVo>> parentPathMap = inquiryMidCategoryApi.queryCategoryParentPathByIds(classifyIds);

            // 组装中台六级分类
            for (InquiryRepeatUseDrugLimitVo inquiryRepeatUseDrugLimitDto : inquiryRepeatUseDrugLimitDtoPageInfoList) {
                List<SaasCategoryVo> categoryVos = new ArrayList<>();
                List<Integer> classifyIdList = inquiryRepeatUseDrugLimitDetailPoListMap.get(inquiryRepeatUseDrugLimitDto.getId());
                if (CollectionUtils.isEmpty(classifyIdList)) {
                    continue;
                }
                classifyIdList.stream().sorted().forEach(classifyId -> {
                    SaasCategoryVo saasCategoryVo = new SaasCategoryVo();
                    saasCategoryVo.setId(classifyId);
                    saasCategoryVo.setPathName(parentPathMap.getOrDefault(classifyId, new ArrayList<>()).stream().map(SaasCategoryVo::getTitle).collect(joining(">")));
                    categoryVos.add(saasCategoryVo);
                });
                inquiryRepeatUseDrugLimitDto.setSaasCategoryVoList(categoryVos);
            }

        }

        return inquiryRepeatUseDrugLimitDtoPageInfo;
    }

    @Override
    public List<List<Integer>> getAllRule(List<Integer> fourthAndFiveAndSixCategoryIdList) {

        if (CollectionUtils.isEmpty(fourthAndFiveAndSixCategoryIdList)) {
            return Lists.newArrayList();
        }

        List<Long> repeatUseDrugLimitIdList = inquiryRepeatUseDrugLimitDetailMapper.queryRepeatUseDrugLimitIdByClassifyIdList(fourthAndFiveAndSixCategoryIdList);

        if (CollectionUtils.isEmpty(repeatUseDrugLimitIdList)) {
            return Lists.newArrayList();
        }

        InquiryRepeatUseDrugLimitQueryVo inquiryRepeatUseDrugLimitQueryDto = InquiryRepeatUseDrugLimitQueryVo.builder().idList(repeatUseDrugLimitIdList).status(CommonStatusEnum.ENABLE.getStatus()).build();

        List<InquiryRepeatUseDrugLimitPo> inquiryRepeatUseDrugLimitPoList = inquiryRepeatUseDrugLimitMapper.queryList(inquiryRepeatUseDrugLimitQueryDto);

        if (CollectionUtils.isEmpty(inquiryRepeatUseDrugLimitPoList)) {
            return Lists.newArrayList();
        }

        List<Long> inquiryRepeatUseDrugLimitPoIdList = inquiryRepeatUseDrugLimitPoList.stream().map(InquiryRepeatUseDrugLimitPo::getId).collect(toList());

        List<InquiryRepeatUseDrugLimitDetailPo> inquiryRepeatUseDrugLimitDetailPoList = inquiryRepeatUseDrugLimitDetailMapper.queryList(InquiryRepeatUseDrugLimitQueryVo.builder().idList(inquiryRepeatUseDrugLimitPoIdList).build());

        Map<Long, List<Integer>> collect = inquiryRepeatUseDrugLimitDetailPoList.stream().collect(groupingBy(InquiryRepeatUseDrugLimitDetailPo::getRepeatUseDrugLimitId,
                mapping(InquiryRepeatUseDrugLimitDetailPo::getClassifyId, toList())));

        return Lists.newArrayList(collect.values());
    }
}