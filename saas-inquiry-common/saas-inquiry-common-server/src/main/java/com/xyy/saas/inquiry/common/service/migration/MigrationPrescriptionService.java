package com.xyy.saas.inquiry.common.service.migration;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationExportVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationImInfoRespVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationInfoRespVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationQueryVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationRespVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationStoreExportVo;
import com.xyy.saas.inquiry.common.mq.message.migration.dto.MigrationPrescriptionEventDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationPrescriptionReqDto;
import com.xyy.saas.inquiry.pojo.migration.PrescriptionMigrationInfoDto;
import java.util.List;

/**
 * @Author:chenxiaoyi
 * @Date:2025/08/20 21:14
 */
public interface MigrationPrescriptionService {

    /**
     * 迁移处方
     *
     * @param reqDto
     */
    void migrationPrescription(MigrationPrescriptionReqDto reqDto);

    /**
     * 迁移处方到es
     *
     * @param msg
     */
    void migrationPrescription2Es(MigrationPrescriptionEventDto msg);

    /**
     * 查询迁移处方（分页）
     *
     * @param queryVo 查询条件
     * @return 分页结果
     */
    PageResult<PrescriptionMigrationInfoDto> pageQueryMigrationPrescription(PrescriptionMigrationQueryVo queryVo);

    /**
     * 查询门店迁移处方（分页）
     *
     * @param reqDto
     * @return
     */
    PageResult<PrescriptionMigrationRespVo> pageStoreQueryMigrationPrescription(PrescriptionMigrationQueryVo reqDto);

    /**
     * 查询迁移处方
     *
     * @param queryVo
     * @return
     */
    PrescriptionMigrationInfoRespVo queryMigrationPrescription(PrescriptionMigrationQueryVo queryVo);

    /**
     * 导出系统查询的迁移处方
     *
     * @param reqDto
     * @return
     */
    List<PrescriptionMigrationExportVo> exportSystemQueryMigrationPrescription(PrescriptionMigrationQueryVo reqDto);

    /**
     * 导出门店查询的迁移处方
     *
     * @param reqDto
     * @return
     */
    List<PrescriptionMigrationStoreExportVo> exportStoreQueryMigrationPrescription(PrescriptionMigrationQueryVo reqDto);

    /**
     * 查询迁移处方的IM信息
     *
     * @param reqDto
     * @return
     */
    List<PrescriptionMigrationImInfoRespVo> queryMigrationPrescriptionImList(PrescriptionMigrationQueryVo reqDto);
}
