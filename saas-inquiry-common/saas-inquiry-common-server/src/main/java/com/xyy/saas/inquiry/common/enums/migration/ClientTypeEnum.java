package com.xyy.saas.inquiry.common.enums.migration;

import lombok.Getter;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 40 app, 70 h5
 */
@Getter
public enum ClientTypeEnum {

    IOS(30, "IOS"),// IOS端服务包专用，与问诊客户端无关
    APP(40, "APP"),
    H5(70, "H5");
    private Integer value;
    private String client;

    private ClientTypeEnum(Integer value, String client) {
        this.value = value;
        this.client = client;
    }

    private static Map<Integer, ClientTypeEnum> CLIENT_TYPE_MAP =
        Stream.of(values()).collect(Collectors.toMap(ClientTypeEnum::getValue, v -> v));
    private static Map<Integer, String> CLIENT_TYPE_NAME_MAP =
        Stream.of(values()).collect(Collectors.toMap(ClientTypeEnum::getValue, ClientTypeEnum::getClient));

    public static ClientTypeEnum getByCode(Integer code) {
        return CLIENT_TYPE_MAP.get(code);
    }

    public static Map<Integer, ClientTypeEnum> getEumMap() {
        return CLIENT_TYPE_MAP;
    }

    public static Map<Integer, String> getEumNameMap() {
        return CLIENT_TYPE_NAME_MAP;
    }
}
