/*
package com.xyy.saas.inquiry.common.config;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

@Slf4j
@Component
public class CosClient implements DisposableBean {

    @Value("${cos.bucketName}")
    String  bucketName;
    @Value("${cos.user}")
    String  user;
    @Value("${cos.secretId}")
    String  secretId;
    @Value("${cos.secretKey}")
    String  secretKey;
    @Value("${cos.area}")
    String  area;
    @Value("${cos.upload.url}")
    String  url;

    private static final String prefix="INVT/Lzinq/";

    private static final Object COS_CLIENT_INIT_MONITOR = new Object();
    private static volatile COSClient cosClient = null;

    public COSClient getClient(){
        if(Objects.isNull(cosClient)){
            synchronized (COS_CLIENT_INIT_MONITOR){
                if(Objects.isNull(cosClient)){
                    COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
                    // 设置 bucket 的区域
                    ClientConfig clientConfig = new ClientConfig(new Region(area));
                    // 配置使用 https
                    clientConfig.setHttpProtocol(HttpProtocol.https);
                    */
/** Error时，最多重试3次 *//*

                    clientConfig.setMaxErrorRetry(3);
                    cosClient = new COSClient(cred, clientConfig);
                }
            }
        }
        return cosClient;
    }


    public Bucket initBucket(){
        log.error("init.CosPropertise.initBucket.Bucket:{};",bucketName);
        try{
            COSClient cosClient = getClient();
            CreateBucketRequest createBucketRequest = new CreateBucketRequest(bucketName);//存储桶名称，格式：BucketName-APPID
            // 设置 bucket 的权限为 Private(私有读写)、其他可选有 PublicRead（公有读私有写）、PublicReadWrite（公有读写）
            createBucketRequest.setCannedAcl(CannedAccessControlList.Private);
            Bucket bucketResult = cosClient.createBucket(createBucketRequest);
            return bucketResult;
        } catch (CosServiceException serverException) {
            log.error("e.CosPropertise.initBucket.Bucket:{};CosServiceException:{}",bucketName,serverException);
        } catch (CosClientException clientException) {
            log.error("e.CosPropertise.initBucket.Bucket:{};CosClientException:{}",bucketName,clientException);
        }
        return null;
    }


    //上传文件
    public String upload(byte[] data, String fileName, Integer type){
        COSClient cosClient=getClient();
        String key = getPathByUUid(fileName,type);
        ObjectMetadata metadate=new ObjectMetadata();
        metadate.setContentLength(data.length);
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key,new ByteArrayInputStream(data),metadate);
        // 设置文件大小大于等于10MB才使用分块上传
        TransferManagerConfiguration transferManagerConfiguration = new TransferManagerConfiguration();
        transferManagerConfiguration.setMultipartUploadThreshold(10*1024*1024);
        TransferManager transferManager=new TransferManager(cosClient);
        transferManager.setConfiguration(transferManagerConfiguration);
        //文件上传
        try{
            Upload upload = transferManager.upload(putObjectRequest);
            // 等待传输结束
            UploadResult uploadResult = upload.waitForUploadResult();
            transferManager.shutdownNow(false);
        }catch (Exception e){
            log.error("e.CosService.upload.fikeKey:{};Exception:{}",key,e);
            return null;
        }
        return url+key;
    }

    //上传过大文件服务
    public String upload2(InputStream inputStream, String fileName,Integer type) {
        String key = getPathByUUid(fileName,type);
        try{
            COSClient cosClient=getClient();
            ObjectMetadata metadata = new ObjectMetadata();
            // 指定文件上传到 COS 上的路径，即对象键。例如对象键为folder/picture.jpg，则表示将文件 picture.jpg 上传到 folder 路径下
            ObjectMetadata objectMetadata = new ObjectMetadata();
            //objectMetadata.setContentLength(is.available());
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, inputStream,objectMetadata);
            PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
        }catch (Exception e) {
            log.error("e.CosService.upload2.fikeKey:{};Exception:{}",key,e);
        }
        return url+key;
    }

    //文件通过file上传
    public String upload3(String localFilePath,Integer type) {
        String key = getPathByUUid(localFilePath,type);
        try{
            COSClient cosClient=getClient();
            // 指定要上传的文件
            File localFile = new File(localFilePath);
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, localFile);
            PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
        }catch (Exception e) {
            log.error("e.CosService.upload2.fikeKey:{};Exception:{}",key,e);
        }
        return url+key;
    }


    //根据文件key下载文件
    public InputStream download(String key){
        COSClient cosClient=getClient();
        GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, key);
        COSObject cosObject = cosClient.getObject(getObjectRequest);
        COSObjectInputStream cosObjectInput = cosObject.getObjectContent();
        try{
            InputStream is= cosObjectInput;
            return is;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

//    //根据key获取临时url
//    public String geturlBykey(String key){
//        COSClient cosClient=getClient();
//        //设置临时url有效时长2小时
//        Date date = new Date(System.currentTimeMillis() + 2 * 60 * 60 * 1000);
//        URL url = cosClient.generatePresignedUrl(bucketName,key,date);
////        cosClient.shutdown();
//        return url.toString();
//    }

    //生成文件路径 如果type=1 表示uuid type=2 表示以当前名字
    public String getPathByUUid(String fileName,Integer type){
        */
/** 截取文件类型 *//*

        int pointIdx = fileName.lastIndexOf(".");
        if (StringUtils.isBlank(fileName) || Objects.equals(pointIdx,-1)) {
            log.error("未知的文件类型，fileNameOrFileType必须包含文件后缀:{}"+fileName);
        }
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyyMMdd");
        String path =simpleDateFormat.format(new Date());
        if(type==2){
            path=simpleDateFormat.format(new Date()) +"/"+ fileName;
            return prefix+path;
        }else{
            String fileSuffix = fileName.substring(pointIdx);
            */
/** 随机文件名 *//*

            String filePrefix = generateUUID();
            path= simpleDateFormat.format(new Date()) +"/"+ filePrefix+ fileSuffix;
            return prefix+path;
        }

    }


    public void deleteFile(String fileVisitUrl) {
        if (StringUtils.isBlank(fileVisitUrl)) {
            return;
        }
        if (Objects.equals(fileVisitUrl.indexOf(url), -1)
                || Objects.equals(StringUtils.lastIndexOf(fileVisitUrl, "."), -1)) {
            log.error("ERROR::CosTransferClient#deleteFile[fileVisitUrl] >>> 文件路径无法识别，不做任何操作 ");
            return;
        }
        log.info("要删除的URL：{}",fileVisitUrl);
        String fileKey = StringUtils.replace(fileVisitUrl, url, "/");
        getClient().deleteObject(bucketName, fileKey);
    }


    public String getPathFileName(String fileName){
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyyMMdd");
        String path =simpleDateFormat.format(new Date()) + "/"+ fileName;
        return prefix+path;
    }


    */
/**
     * InputStream 转换成byte[]
     * @param input
     * @return
     * @throws IOException
     *//*

    public byte[] toByteArray(InputStream input) throws IOException {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024 * 4];
        int n = 0;
        while (-1 != (n = input.read(buffer))) {
            output.write(buffer, 0, n);
        }
        return output.toByteArray();
    }

    private String generateUUID(){
        return StringUtils.replace(UUID.randomUUID().toString(), "-", "");
    }

    public String  saveFile(SXSSFWorkbook workbook, String fileName) {
        String path="";
        byte [] bookByteAry =null;
        try{
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            bookByteAry = out.toByteArray();
        }
        catch (Exception e){
            log.error("批量导入门店信息，解析失败文件错误，e:{}",e);
            return path;
        }
        return upload(bookByteAry,generateUUID()+".xlsx",2);
    }

    public InputStream queryFdfsFile(String path){
        InputStream is=null;
        try{
            String fileKey = StringUtils.replace(path,url,"/");
            is=download(fileKey);
        }catch (Exception e){
            log.error("获取cos中的文件异常",e);
        }
        return is;
    }

    public String  saveWorkBookFile(Workbook workbook, String fileName){
        String path="";
        byte [] bookByteAry =null;
        try{
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            bookByteAry = out.toByteArray();
        }
        catch (Exception e){
            log.error("批量导入门店信息，解析失败文件错误，e:{}",e);
            return path;
        }
        return upload(bookByteAry,generateUUID()+".xlsx",2);
    }

    public Boolean deleteFileByUrl(String group,String path){
        boolean back = true;
        try {
            deleteFile(path);
            log.info("删除文件，文件:{} ",group+"/"+path);
        }catch (Exception e){
            log.info("删除文件异常，文件:{} ",group+"/"+path);
            back= false;
        }
        return back;
    }

    public String saveFile(ByteArrayOutputStream outputIo, String fileName){
        return upload(outputIo.toByteArray(),fileName,1);
    }

    public String saveLocalFile(String localFile){
        return upload3(localFile,1);
    }


    public String getFileName(String url){
        String path = null;
        if(StringUtils.isNotBlank(url)){
            String[] split = url.split("/");
            if(split.length>0){
                path = split[split.length-1];
            }
        }
        return path;
   }

    */
/**
     * 资源清理
     * @throws Exception
     *//*

    public void destroy() throws Exception {
        */
/** 清理cosClient *//*

        if(Objects.nonNull(cosClient))
            cosClient.shutdown();
    }


    public static void main(String[] args) {
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyyMMdd");
        String path =simpleDateFormat.format(new Date()) + "/"+ UUID.randomUUID().toString().replace("-", "");
        System.out.println(path);
    }

}
*/
