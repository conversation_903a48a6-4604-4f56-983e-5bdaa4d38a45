package com.xyy.saas.inquiry.common.controller.admin.rational;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.inquiry.common.api.rational.LimitDrugCatalogApi;
import com.xyy.saas.inquiry.common.api.rational.dto.LimitDrugCatalogDto;
import com.xyy.saas.inquiry.common.api.rational.dto.LimitDrugCatalogSaveDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.LimitDrugCatalogOperationVo;
import com.xyy.saas.inquiry.common.enums.rational.LimitDrugCatalogNameTypeEnum;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 目录限制用药
 */
@Tag(name = "管理后台 - 合理用药 - 限制类用药")
@RestController
@RequestMapping("/limitDrug/catalog")
@Validated
public class LimitDrugCatalogController {

    @Resource
    private LimitDrugCatalogApi limitDrugCatalogApi;

    /**
     * 保存
     *
     * @param limitDrugCatalogSaveDto
     * @return com.xyy.saas.remote.web.core.utils.ResultVO<java.lang.Boolean>
     * <AUTHOR> 4/25/24 5:49 PM
     */
    @PostMapping(value = "/save")
    CommonResult<Boolean> save(@RequestBody LimitDrugCatalogSaveDto limitDrugCatalogSaveDto) {

        if (!LimitDrugCatalogNameTypeEnum.LIMIT_CATALOG.getCode().equals(limitDrugCatalogSaveDto.getLimitType())
                && !LimitDrugCatalogNameTypeEnum.LIMIT_WHITE_CATALOG.getCode().equals(limitDrugCatalogSaveDto.getLimitType())) {
            return CommonResult.error("限制药品目录名称类型错误");
        }

        return limitDrugCatalogApi.save(limitDrugCatalogSaveDto);
    }

    /**
     * 分页查询
     *
     * @param limitDrugCatalogOperationDto
     * @return com.xyy.saas.remote.web.core.utils.ResultVO<java.lang.Boolean>
     * <AUTHOR> 4/25/24 5:49 PM
     */
    @PostMapping(value = "/pageQuery")
    CommonResult<PageResult<LimitDrugCatalogDto>> pageQuery(@RequestBody LimitDrugCatalogOperationVo limitDrugCatalogOperationDto) {

        if (!LimitDrugCatalogNameTypeEnum.LIMIT_CATALOG.getCode().equals(limitDrugCatalogOperationDto.getLimitType())
                && !LimitDrugCatalogNameTypeEnum.LIMIT_WHITE_CATALOG.getCode().equals(limitDrugCatalogOperationDto.getLimitType())) {
            return CommonResult.error("限制药品目录名称类型错误");
        }

        return limitDrugCatalogApi.pageQuery(limitDrugCatalogOperationDto);
    }

    /**
     * 删除
     *
     * @param limitDrugCatalogOperationDto
     * @return com.xyy.saas.remote.web.core.utils.ResultVO<java.lang.Boolean>
     * <AUTHOR> 4/25/24 5:49 PM
     */
    @PostMapping(value = "/deleteByIdList")
    CommonResult<Boolean> deleteByIdList(@RequestBody LimitDrugCatalogOperationVo limitDrugCatalogOperationDto) {

        return limitDrugCatalogApi.deleteByIdList(limitDrugCatalogOperationDto);
    }

    /**
     * 查询设置
     *
     * @return com.xyy.saas.remote.web.core.utils.ResultVO<java.lang.Boolean>
     * <AUTHOR> 4/25/24 5:49 PM
     */
    @GetMapping(value = "/getConfig")
    CommonResult<LimitDrugCatalogOperationVo> getConfig() {

        return limitDrugCatalogApi.getConfig();
    }

    /**
     * 保存或修改设置
     *
     * @param limitDrugCatalogOperationDto
     * @return com.xyy.saas.remote.web.core.utils.ResultVO<java.lang.Boolean>
     * <AUTHOR> 4/25/24 5:49 PM
     */
    @PostMapping(value = "/saveOrUpdateConfig")
    CommonResult<Boolean> saveOrUpdateConfig(@RequestBody LimitDrugCatalogOperationVo limitDrugCatalogOperationDto) {


        if (limitDrugCatalogOperationDto.getCautionLevel() == null || StringUtils.isBlank(limitDrugCatalogOperationDto.getCautionDesc())) {
            return CommonResult.error("限制级别和提示语不能为空");
        }

        return limitDrugCatalogApi.saveOrUpdateConfig(limitDrugCatalogOperationDto);
    }

}
