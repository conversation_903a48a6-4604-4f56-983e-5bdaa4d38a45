package com.xyy.saas.inquiry.common.service.migration;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_BIND_OLD_HY_HAS_NO_BIND_FAIL;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery.Builder;
import co.elastic.clients.elasticsearch._types.query_dsl.RangeQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TermQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TermsQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TermsQueryField;
import co.elastic.clients.elasticsearch._types.query_dsl.WildcardQuery;
import co.elastic.clients.json.JsonData;
import com.xyy.saas.inquiry.annotation.InquiryDateType;
import com.xyy.saas.inquiry.common.constant.MigrationConstant;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationExportVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationImInfoRespVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationInfoRespVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationQueryVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationRespVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationStoreExportVo;
import com.xyy.saas.inquiry.common.convert.migration.MigrationConvert;
import com.xyy.saas.inquiry.common.enums.migration.PerscriptionStatusEnum;
import com.xyy.saas.inquiry.common.enums.migration.PrescriptionSourceExtEnum;
import com.xyy.saas.inquiry.common.mq.message.migration.MigrationPrescriptionEvent;
import com.xyy.saas.inquiry.common.mq.message.migration.dto.MigrationPrescriptionEventDto;
import com.xyy.saas.inquiry.common.mq.producer.migration.MigrationPrescription2EsMQProducer;
import com.xyy.saas.inquiry.common.util.SwitchUtil;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.pharmacist.api.migration.PrescriptionMigrationApi;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationPrescriptionReqDto;
import com.xyy.saas.inquiry.pojo.migration.PrescriptionMigrationDetailDto;
import com.xyy.saas.inquiry.pojo.migration.PrescriptionMigrationInfoDto;
import com.xyy.saas.inquiry.util.ThreadPoolManager;
import com.xyy.saas.inquiry.util.TimeWatchUtil;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.IndexedObjectInformation;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.core.query.IndexQueryBuilder;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

/**
 * 处方迁移服务实现类
 *
 * @Author:chenxiaoyi
 * @Date:2025/08/20 21:14
 */
@Slf4j
@Service
public class MigrationPrescriptionServiceImpl implements MigrationPrescriptionService {


    @Resource
    private PrescriptionMigrationApi prescriptionMigrationApi;

    @Resource
    protected StringRedisTemplate stringRedisTemplate;

    @Resource
    private ElasticsearchOperations elasticsearchOperations;

    @Resource
    private MigrationPrescription2EsMQProducer migrationPrescription2EsMQProducer;

    @Resource
    private ConfigApi configApi;

    @Resource
    private MigrationPrescriptionFailRecordService failRecordService;

    @Autowired
    private TenantApi tenantApi;

    /**
     * 获取迁移延迟时间 默认1000ms
     */
    public Long getMigrationDelayTime() {
        return NumberUtils.toLong(configApi.getConfigValueByKey(MigrationConstant.MIGRATION_PRESCRIPTION_DELAY_TIME), 500);
    }

    /**
     * 获取迁移分页大小 默认1000
     */
    private Integer getMigrationPageSize() {
        return NumberUtils.toInt(configApi.getConfigValueByKey(MigrationConstant.MIGRATION_PRESCRIPTION_PAGE_SIZE), 1000);
    }

    /**
     * 获取迁移开关 默认开启
     */
    private boolean getMigrationSwitch() {
        String byKey = configApi.getConfigValueByKey(MigrationConstant.MIGRATION_PRESCRIPTION_SWITCH);
        return StringUtils.isBlank(byKey) || StringUtils.equals(byKey, "1");
    }


    @Override
    public void migrationPrescription(MigrationPrescriptionReqDto reqDto) {
        log.info("开始处方迁移任务，参数：{}", JSONUtil.toJsonStr(reqDto));

        Boolean b = stringRedisTemplate.opsForValue().setIfAbsent(MigrationConstant.MIGRATION_PRESCRIPTION_FLAG_KEY, "1");
        if (Boolean.FALSE.equals(b)) {
            throw new RuntimeException("处方迁移任务正在执行中，请勿重复执行");
        }
        // 执行迁移id分发
        ThreadPoolManager.execute(() -> executeMigrationDistribution(reqDto));
    }

    // 开始执行迁移
    private void executeMigrationDistribution(MigrationPrescriptionReqDto reqDto) {

        Long minId = reqDto.getMinId();
        Long maxId = reqDto.getMaxId();
        String organSign = reqDto.getOrganSign();
        String rangeId = minId + "-" + maxId;

        try {
            // 根据 minId 按照 getMigrationPageSize() 步长递增累加
            Long currentMinId = minId;
            while (currentMinId < maxId) {
                // 检查迁移开关 - 在每次循环中判断
                if (!getMigrationSwitch()) {
                    log.warn("处方迁移开关已关闭，停止迁移任务");
                    break;
                }
                Long currentMaxId = Math.min(currentMinId + getMigrationPageSize(), maxId);
                // 创建分页消息
                MigrationPrescriptionEventDto eventDto = new MigrationPrescriptionEventDto()
                    .setMinId(currentMinId)
                    .setMaxId(currentMaxId)
                    .setRangeId(rangeId)
                    .setOrganSign(organSign);

                // migrationPrescription2Es(eventDto);
                // 发送MQ消息
                migrationPrescription2EsMQProducer.sendMessage(
                    MigrationPrescriptionEvent.builder().msg(eventDto).build()
                );

                log.info("发送处方迁移MQ消息，ID范围：{}-{}，机构：{}", currentMinId, currentMaxId, organSign);

                // 延迟处理，避免对系统造成过大压力
                if (getMigrationDelayTime() > 0) {
                    try {
                        Thread.sleep(getMigrationDelayTime());
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.error("处方迁移延迟等待被中断", e);
                        break;
                    }
                }
                currentMinId = currentMaxId;
            }
            log.info("处方迁移任务分发完成，总范围：{}-{},机构：{}", minId, maxId, organSign);

        } catch (Exception e) {
            log.error("处方迁移任务执行失败，参数：{}", JSONUtil.toJsonStr(reqDto), e);
            // 记录失败信息到数据库
            failRecordService.createMqFailRecord(organSign, minId, maxId, e.getMessage());
        } finally {
            stringRedisTemplate.delete(MigrationConstant.MIGRATION_PRESCRIPTION_FLAG_KEY);
        }

    }


    @Override
    public void migrationPrescription2Es(MigrationPrescriptionEventDto msg) {
        // 检查迁移开关
        if (!getMigrationSwitch()) {
            log.warn("处方迁移开关已关闭，跳过ES迁移任务");
            return;
        }
        Long minId = msg.getMinId();
        Long maxId = msg.getMaxId();
        String organSign = msg.getOrganSign();

        log.info("处方迁移到ES，ID范围：{}-{},机构：{}", minId, maxId, organSign);

        try {
            incrementMigrationRedisCount(msg.getRangeId(), maxId, minId);

            // 执行老系统的查询
            List<PrescriptionMigrationInfoDto> migrationInfoDtos = TimeWatchUtil.excute(() -> prescriptionMigrationApi
                .queryPrescriptionMigrationInfo(MigrationConvert.INSTANCE.convertDto(msg)), "处方迁移到ES 查询老系统处方");

            if (CollUtil.isEmpty(migrationInfoDtos)) {
                log.info("处方迁移到ES未查询到处方数据，ID范围：{}-{},机构：{}", minId, maxId, organSign);
                return;
            }

            // 批量写入ES，支持更新已存在文档
            int successCount = TimeWatchUtil.excute(() -> batchSaveToEs(migrationInfoDtos), "处方迁移到ES 批量写入ES");

            log.info("处方迁移到ES完成，ID范围：{}-{} , 机构：{}，成功数量：{}", minId, maxId, organSign, successCount);

            failRecordService.createEsSuccessRecord(organSign, minId, maxId, successCount);

        } catch (Exception e) {
            log.error("处方迁移到ES，失败，ID范围：{}-{}, 机构：{}", minId, maxId, organSign, e);
            failRecordService.createEsFailRecord(organSign, minId, maxId, e.getMessage());
        }
    }

    // Redis键定义 累加迁移数量
    private void incrementMigrationRedisCount(String rangId, Long maxId, Long minId) {
        String migrationCountKey = MigrationConstant.MIGRATION_PRESCRIPTION_ES_PROGRESS_KEY + rangId;
        stringRedisTemplate.opsForValue().increment(migrationCountKey, maxId - minId);
        stringRedisTemplate.expire(migrationCountKey, 30, TimeUnit.DAYS);
    }

    /**
     * 根据创建时间获取ES索引名称 格式：inquiry_history_prescription_yyyy
     */
    private String getIndexNameByCreateTime(java.util.Date createTime) {
        if (createTime == null) {
            // 如果创建时间为空，使用当前年份
            return MigrationConstant.HISTORY_PRESCRIPTION_INDEX_PREFIX + LocalDateTime.now().getYear();
        }

        // 提取年份
        LocalDateTime dateTime = createTime.toInstant()
            .atZone(java.time.ZoneId.systemDefault())
            .toLocalDateTime();

        return MigrationConstant.HISTORY_PRESCRIPTION_INDEX_PREFIX + dateTime.getYear();
    }

    /**
     * 根据机构号获取路由键 直接使用organSign作为路由键，确保数据分布的唯一性
     */
    private String getRoutingByOrganSign(String organSign) {
        if (StrUtil.isBlank(organSign)) {
            return "default";
        }

        // 直接使用机构号作为路由键，保证同一机构的数据路由到同一分片
        return organSign;
    }

    /**
     * 批量保存数据到ES，支持更新已存在文档 优化：根据创建时间区分索引，根据机构号设置路由，按索引+路由进行二级分组
     */
    private int batchSaveToEs(List<PrescriptionMigrationInfoDto> migrationInfoDtos) {
        if (CollUtil.isEmpty(migrationInfoDtos)) {
            log.warn("处方迁移到ES 数据为空，跳过处理");
            return 0;
        }

        int successCount = 0;

        // 按索引+路由进行二级分组，确保相同路由的数据批量写入到同一分片
        Map<String, Map<String, List<PrescriptionMigrationInfoDto>>> indexRoutingGroupMap = migrationInfoDtos.stream()
            .collect(Collectors.groupingBy(
                info -> getIndexNameByCreateTime(info.getCreateTime()),
                Collectors.groupingBy(info -> getRoutingByOrganSign(info.getOrganSign()))
            ));

        // log.info("处方迁移到ES 开始批量保存，总数量：{}，索引数量：{}", migrationInfoDtos.size(), indexRoutingGroupMap.size());

        // 遍历每个索引
        for (Map.Entry<String, Map<String, List<PrescriptionMigrationInfoDto>>> indexEntry : indexRoutingGroupMap.entrySet()) {
            String indexName = indexEntry.getKey();
            Map<String, List<PrescriptionMigrationInfoDto>> routingGroupMap = indexEntry.getValue();

            // 确保索引存在
            IndexCoordinates indexCoordinates = IndexCoordinates.of(indexName);
            IndexOperations indexOps = elasticsearchOperations.indexOps(indexCoordinates);
            if (!indexOps.exists()) {
                log.info("处方迁移到ES 没有 ES索引 跳过：{}", indexName);
                continue;
            }

            // 遍历每个路由分组
            for (Map.Entry<String, List<PrescriptionMigrationInfoDto>> routingEntry : routingGroupMap.entrySet()) {
                String routing = routingEntry.getKey();
                List<PrescriptionMigrationInfoDto> routingData = routingEntry.getValue();

                try {
                    // 批量构建文档 - 使用save操作支持更新
                    List<IndexQuery> indexQueries = new ArrayList<>();
                    for (PrescriptionMigrationInfoDto prescriptionInfo : routingData) {
                        // 验证必要字段
                        if (prescriptionInfo.getId() == null) {
                            log.warn("处方迁移到ES 跳过无效数据，ID为空：{}", prescriptionInfo.getGuid());
                            continue;
                        }

                        IndexQuery indexQuery = new IndexQueryBuilder()
                            .withId(String.valueOf(prescriptionInfo.getId()))
                            .withObject(prescriptionInfo)
                            .withRouting(routing)
                            .build();
                        indexQueries.add(indexQuery);
                    }

                    if (CollUtil.isEmpty(indexQueries)) {
                        log.warn("处方迁移到ES 索引：{}，路由：{} 无有效数据", indexName, routing);
                        continue;
                    }

                    // 批量写入ES（如果文档存在则更新）
                    List<IndexedObjectInformation> results = elasticsearchOperations.bulkIndex(indexQueries, indexCoordinates);
                    successCount += results.size();

                    // log.info("处方迁移到ES 批量保存ES成功，索引：{}，路由：{}，数量：{}/{}",
                    //     indexName, routing, results.size(), routingData.size());

                } catch (Exception e) {
                    log.error("处方迁移到ES 批量保存ES失败，索引：{}，路由：{}，数量：{}，错误：{}",
                        indexName, routing, routingData.size(), e.getMessage(), e);
                    throw e;
                }
            }
        }

        return successCount;
    }

    @Override
    @InquiryDateType
    public PageResult<PrescriptionMigrationRespVo> pageStoreQueryMigrationPrescription(PrescriptionMigrationQueryVo reqDto) {

        handleStoreQueryVo(reqDto);

        PageResult<PrescriptionMigrationInfoDto> pageResult = pageQueryMigrationPrescription(reqDto);

        if (CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty();
        }
        return new PageResult<>(MigrationConvert.INSTANCE.convertRespVo(pageResult.getList()), pageResult.getTotal());
    }

    @Override
    public PageResult<PrescriptionMigrationInfoDto> pageQueryMigrationPrescription(PrescriptionMigrationQueryVo queryVo) {

        setQueryOrganSignRoute(queryVo);

        try {
            // 1. 根据年份确定索引名称
            String indexName = getIndexNameByYear(queryVo.getYear());
            IndexCoordinates indexCoordinates = IndexCoordinates.of(indexName);

            // 2. 检查索引是否存在
            IndexOperations indexOps = elasticsearchOperations.indexOps(indexCoordinates);
            if (!indexOps.exists()) {
                log.warn("查询ES处方数据 索引不存在：{}", indexName);
                return new PageResult<>();
            }

            // 4. 创建分页请求，按创建时间降序排序
            PageRequest pageRequest = PageRequest.of(
                queryVo.getPageNo() - 1,
                queryVo.getPageSize(),
                Sort.by(Sort.Direction.DESC, "id")
            );

            // 使用原生查询，支持精确的 wildcard 模糊匹配
            co.elastic.clients.elasticsearch._types.query_dsl.Query nativeEsQuery = buildNativeSearchQuery(queryVo);
            org.springframework.data.elasticsearch.core.query.Query query = NativeQuery.builder()
                .withQuery(nativeEsQuery)
                .withPageable(pageRequest)
                .withTrackTotalHits(TenantConstant.isSystemTenant() ? null : true)
                .withRoute(StringUtils.isNotBlank(queryVo.getOrganSign()) ? getRoutingByOrganSign(queryVo.getOrganSign()) : null)
                .build();

            log.info("查询 DSL语句：{}", nativeEsQuery);

            // 打印查询条件用于调试
            log.info("查询ES处方数据 索引：{}，机构号：{}，年份：{}，分页：{}/{}",
                indexName, queryVo.getOrganSign(), queryVo.getYear(), queryVo.getPageNo(), queryVo.getPageSize());

            // 6. 执行查询
            SearchHits<PrescriptionMigrationInfoDto> searchHits = elasticsearchOperations.search(
                query, PrescriptionMigrationInfoDto.class, indexCoordinates
            );

            if (searchHits.isEmpty()) {
                return new PageResult<>();
            }

            // 8. 处理查询结果
            List<PrescriptionMigrationInfoDto> results = new ArrayList<>();
            for (SearchHit<PrescriptionMigrationInfoDto> hit : searchHits) {
                results.add(hit.getContent());
            }

            // 9. 构建分页结果
            PageResult<PrescriptionMigrationInfoDto> pageResult = new PageResult<>();
            pageResult.setTotal(searchHits.getTotalHits());
            pageResult.setList(results);
            return pageResult;

        } catch (Exception e) {
            log.error("查询ES处方数据 失败，参数：{}，错误：{}", JSONUtil.toJsonStr(queryVo), e.getMessage(), e);
            return new PageResult<>();
        }
    }


    private void setQueryOrganSignRoute(PrescriptionMigrationQueryVo queryVo) {
        if (!TenantConstant.isSystemTenant()) {
            TenantDto tenantDto = tenantApi.getTenant();
            if (tenantDto == null || tenantDto.getExt() == null || StringUtils.isBlank(tenantDto.getExt().getOrganSign())) {
                throw exception(TENANT_BIND_OLD_HY_HAS_NO_BIND_FAIL);
            }
            queryVo.setOrganSign(tenantDto.getExt().getOrganSign());
        }
    }


    @Override
    @InquiryDateType
    public PrescriptionMigrationInfoRespVo queryMigrationPrescription(PrescriptionMigrationQueryVo queryVo) {

        PageResult<PrescriptionMigrationInfoDto> pageResult = pageQueryMigrationPrescription(queryVo);

        if (pageResult.getList().isEmpty()) {
            log.warn("查询ES单个处方数据 未找到结果：ID={}, pref={}", queryVo.getId(), queryVo.getPref());
            return null;
        }

        PrescriptionMigrationInfoDto infoDto = pageResult.getList().getFirst();

        return MigrationConvert.INSTANCE.convertVo(infoDto);
    }


    /**
     * 根据年份获取索引名称
     */
    private String getIndexNameByYear(String year) {
        if (StrUtil.isBlank(year)) {
            // 如果年份为空，使用当前年份
            year = String.valueOf(LocalDateTime.now().getYear());
        }
        return MigrationConstant.HISTORY_PRESCRIPTION_INDEX_PREFIX + year;
    }


    /**
     * 构建原生 Elasticsearch 查询 使用 BoolQuery 组合多个条件，支持精确的模糊查询
     */
    private co.elastic.clients.elasticsearch._types.query_dsl.Query buildNativeSearchQuery(PrescriptionMigrationQueryVo queryVo) {
        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();
        // ID相关查询
        buildQueryMust(boolQueryBuilder, "id", queryVo.getId());
        buildQueryMust(boolQueryBuilder, "pref", queryVo.getPref());
        buildQueryMust(boolQueryBuilder, "organSign", queryVo.getOrganSign());
        if (CollUtil.isNotEmpty(queryVo.getIds())) {
            boolQueryBuilder.must(TermQuery.of(t -> t.field("id").value(queryVo.getIds().get(0)))._toQuery());
        }
        // 文本模糊查询 - 使用 wildcard 查询实现 SQL LIKE '%value%' 效果
        buildQueryWildcard(boolQueryBuilder, "drugstoreName", queryVo.getDrugstoreName());
        buildQueryWildcard(boolQueryBuilder, "patientName", queryVo.getPatientName());
        buildQueryWildcard(boolQueryBuilder, "productName.keyword", queryVo.getProductName());
        buildQueryWildcard(boolQueryBuilder, "pharmacistName", queryVo.getPharmacistName());
        buildQueryWildcard(boolQueryBuilder, "telephone", queryVo.getTelephone());
        buildQueryWildcard(boolQueryBuilder, "inquiryNo", queryVo.getInquiryNo());
        // = 其他精确匹配查询
        buildQueryMust(boolQueryBuilder, "inquiryGuid", queryVo.getInquiryGuid());
        buildQueryMust(boolQueryBuilder, "inquiryStatus", queryVo.getInquiryStatus());
        buildQueryMust(boolQueryBuilder, "auditStatus", queryVo.getAuditStatus());
        buildQueryMust(boolQueryBuilder, "auditType", queryVo.getAuditType());
        buildQueryMust(boolQueryBuilder, "medicineType", queryVo.getMedicineType());
        buildQueryMust(boolQueryBuilder, "type", queryVo.getType());
        buildQueryMust(boolQueryBuilder, "source", queryVo.getSource());
        buildQueryMust(boolQueryBuilder, "clientType", queryVo.getClientType());
        buildQueryMust(boolQueryBuilder, "inquirySource", queryVo.getInquirySource());
        // !=
        if (queryVo.getNeAuditType() != null) {
            boolQueryBuilder.mustNot(TermQuery.of(t -> t.field("auditType").value(queryVo.getNeAuditType()))._toQuery());
        }
        if (queryVo.getNeqType() != null) {
            boolQueryBuilder.mustNot(TermQuery.of(t -> t.field("type").value(queryVo.getNeqType()))._toQuery());
        }
        // 处方状态（支持单个和列表）
        buildListMust(queryVo, boolQueryBuilder);
        // 时间范围查询
        buildTime(queryVo, boolQueryBuilder);
        return boolQueryBuilder.build()._toQuery();
    }

    private static void buildTime(PrescriptionMigrationQueryVo queryVo, Builder boolQueryBuilder) {
        // 时间范围查询
        if (queryVo.getOutPrescriptionTime() != null && queryVo.getOutPrescriptionTime().length == 2) {
            LocalDateTime startTime = queryVo.getOutPrescriptionTime()[0];
            LocalDateTime endTime = queryVo.getOutPrescriptionTime()[1];
            if (startTime != null && endTime != null) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                boolQueryBuilder.must(RangeQuery.of(r -> r
                    .field("outPrescriptionTime")
                    .gte(JsonData.of(startTime.format(formatter)))
                    .lte(JsonData.of(endTime.format(formatter)))
                )._toQuery());
            }
        }

        if (queryVo.getApprovalTime() != null && queryVo.getApprovalTime().length == 2) {
            LocalDateTime startTime = queryVo.getApprovalTime()[0];
            LocalDateTime endTime = queryVo.getApprovalTime()[1];

            if (startTime != null && endTime != null) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                boolQueryBuilder.must(RangeQuery.of(r -> r
                    .field("approvalTime")
                    .gte(JsonData.of(startTime.format(formatter)))
                    .lte(JsonData.of(endTime.format(formatter)))
                )._toQuery());

            }
        }
    }

    private static void buildListMust(PrescriptionMigrationQueryVo queryVo, Builder boolQueryBuilder) {
        if (queryVo.getStatus() != null) {
            boolQueryBuilder.must(TermQuery.of(t -> t.field("status").value(queryVo.getStatus()))._toQuery());
        } else if (CollUtil.isNotEmpty(queryVo.getStatusList())) {
            List<String> statusStrList = queryVo.getStatusList().stream()
                .map(String::valueOf)
                .collect(Collectors.toList());
            boolQueryBuilder.filter(TermsQuery.of(t -> t
                .field("status")
                .terms(TermsQueryField.of(tf -> tf.value(
                    statusStrList.stream()
                        .map(v -> FieldValue.of(v))
                        .collect(Collectors.toList())
                )))
            )._toQuery());
        }

        // 审批状态
        if (queryVo.getAuditStatus() != null) {
            boolQueryBuilder.must(TermQuery.of(t -> t.field("auditStatus").value(queryVo.getAuditStatus()))._toQuery());
        } else if (CollUtil.isNotEmpty(queryVo.getAuditStatusList())) {
            List<String> statusStrList = queryVo.getAuditStatusList().stream()
                .map(String::valueOf)
                .collect(Collectors.toList());
            boolQueryBuilder.filter(TermsQuery.of(t -> t
                .field("auditStatus")
                .terms(TermsQueryField.of(tf -> tf.value(
                    statusStrList.stream()
                        .map(v -> FieldValue.of(v))
                        .collect(Collectors.toList())
                )))
            )._toQuery());
        }
    }

    private void buildQueryMust(Builder boolQueryBuilder, String field, String value) {
        if (StrUtil.isNotBlank(value)) {
            boolQueryBuilder.must(TermQuery.of(t -> t.field(field).value(value))._toQuery());
        }
    }

    private void buildQueryMust(Builder boolQueryBuilder, String field, Integer value) {
        if (value != null) {
            boolQueryBuilder.must(TermQuery.of(t -> t.field(field).value(value))._toQuery());
        }
    }

    private void buildQueryMust(Builder boolQueryBuilder, String field, Long value) {
        if (value != null) {
            boolQueryBuilder.must(TermQuery.of(t -> t.field(field).value(value))._toQuery());
        }
    }

    private void buildQueryWildcard(BoolQuery.Builder boolQueryBuilder, String field, String value) {
        if (StringUtils.isNotBlank(value)) {
            String escapedValue = SwitchUtil.replaceEsWildLikeStr(value);
            String wildcardValue = "*" + escapedValue + "*";
            boolQueryBuilder.filter(WildcardQuery.of(w -> w
                .field(field)
                .value(wildcardValue)
            )._toQuery());
        }
    }


    @Override
    public List<PrescriptionMigrationExportVo> exportSystemQueryMigrationPrescription(PrescriptionMigrationQueryVo reqDto) {
        reqDto.setPageSize(5000);
        PageResult<PrescriptionMigrationInfoDto> pageResult = pageQueryMigrationPrescription(reqDto);

        if (CollUtil.isEmpty(pageResult.getList())) {
            return List.of();
        }

        List<PrescriptionMigrationExportVo> resultList = new ArrayList<>();

        for (PrescriptionMigrationInfoDto info : pageResult.getList()) {
            try {
                // 1. 基础转换并填充中药信息
                PrescriptionMigrationExportVo baseVo = MigrationConvert.INSTANCE.convertExportVo(info);
                MigrationConvert.INSTANCE.fillTcmInfo(baseVo, info.getDosageAndUsage());

                // 2. 解析处方明细
                List<PrescriptionMigrationDetailDto> details = getPrescriptionMigrationDetailDtos(info);

                // 3. 生成导出记录
                if (CollUtil.isEmpty(details)) {
                    resultList.add(baseVo);
                } else {
                    for (int i = 0; i < details.size(); i++) {
                        PrescriptionMigrationExportVo exportVo = i == 0 ? baseVo : new PrescriptionMigrationExportVo();
                        if (i > 0) {
                            exportVo.setPref(baseVo.getPref());
                        }
                        MigrationConvert.INSTANCE.fillProductInfo(exportVo, details.get(i));
                        resultList.add(exportVo);
                    }
                }
            } catch (Exception e) {
                log.error("处理处方失败，ID：{}", info.getId(), e);
                resultList.add(MigrationConvert.INSTANCE.convertExportVo(info));
            }
        }

        return resultList;
    }

    @Override
    public List<PrescriptionMigrationStoreExportVo> exportStoreQueryMigrationPrescription(PrescriptionMigrationQueryVo reqDto) {
        reqDto.setPageSize(5000);
        handleStoreQueryVo(reqDto);

        PageResult<PrescriptionMigrationInfoDto> pageResult = pageQueryMigrationPrescription(reqDto);

        if (CollUtil.isEmpty(pageResult.getList())) {
            return List.of();
        }

        List<PrescriptionMigrationExportVo> resultList = new ArrayList<>();

        for (PrescriptionMigrationInfoDto info : pageResult.getList()) {
            try {
                // 解析处方明细
                List<PrescriptionMigrationDetailDto> details = getPrescriptionMigrationDetailDtos(info);

                // 生成导出记录 - 每行都显示完整处方信息
                if (CollUtil.isEmpty(details)) {
                    PrescriptionMigrationExportVo exportVo = MigrationConvert.INSTANCE.convertExportVo(info);
                    MigrationConvert.INSTANCE.fillTcmInfo(exportVo, info.getDosageAndUsage());
                    resultList.add(exportVo);
                } else {
                    for (PrescriptionMigrationDetailDto detail : details) {
                        // 每个明细都创建完整的处方信息副本
                        PrescriptionMigrationExportVo exportVo = MigrationConvert.INSTANCE.convertExportVo(info);
                        MigrationConvert.INSTANCE.fillTcmInfo(exportVo, info.getDosageAndUsage());
                        MigrationConvert.INSTANCE.fillProductInfo(exportVo, detail);
                        resultList.add(exportVo);
                    }
                }
            } catch (Exception e) {
                log.error("处理处方失败，ID：{}", info.getId(), e);
                resultList.add(MigrationConvert.INSTANCE.convertExportVo(info));
            }
        }

        return MigrationConvert.INSTANCE.convertStoreExcelVos(resultList);
    }


    @Override
    public List<PrescriptionMigrationImInfoRespVo> queryMigrationPrescriptionImList(PrescriptionMigrationQueryVo reqDto) {
        PrescriptionMigrationInfoRespVo infoRespVo = queryMigrationPrescription(reqDto);
        if (infoRespVo == null) {
            return List.of();
        }

        // 构建聊天内容
        // 1. 请问，您是否在线下医疗机构就诊过，并使用过此次预购药品？

        // 2. 是

        // 3. 请问，您是否有过敏史?

        // 4. 根据allergySymptomExplain判断 没有就是无

        // 5.  请问，您的肝肾功能有无异常？

        // 6. 根据 liverAndRenalFunction 判断 0无 1肝 2肾功 3 肝肾

        // 7. 用药申请
        /**
         * 茉莉 7 男 21
         * 病情描述：咳嗽
         * 诊断：肺坏疽或坏死
         * 过敏史：无
         * 肝、肾功能异常：无异常
         * 用药类型：西药
         * 预购药品：静心颗粒 X 1
         * 共1种药
         */

        // 8.消息

        // 9.您好，您的处方已开具，请查收

        // 10.温馨提示: 用药过程中如有不适，请及时就医，不适随诊

        return List.of();
    }

    private static List<PrescriptionMigrationDetailDto> getPrescriptionMigrationDetailDtos(PrescriptionMigrationInfoDto info) {
        List<PrescriptionMigrationDetailDto> details = Collections.emptyList();
        if (StrUtil.isNotBlank(info.getPrescriptionDetails())) {
            try {
                details = JSONUtil.toList(info.getPrescriptionDetails(), PrescriptionMigrationDetailDto.class);
            } catch (Exception ignored) {
            }
        }
        return details;
    }


    private void handleStoreQueryVo(PrescriptionMigrationQueryVo reqDto) {
        if (reqDto.getStatus() != null && reqDto.getStatus().equals(PerscriptionStatusEnum.STATUS_11.getCode())) {
            List<Integer> statusList = new ArrayList<>();
            List<Integer> tempStatusList = Arrays.stream(PerscriptionStatusEnum.values()).map(PerscriptionStatusEnum::getCode).collect(Collectors.toList());
            tempStatusList.remove(PerscriptionStatusEnum.STATUS_11.getCode());
            tempStatusList.remove(PerscriptionStatusEnum.CREATE.getCode());
            tempStatusList.remove(PerscriptionStatusEnum.CLOSED.getCode());
            tempStatusList.remove(PerscriptionStatusEnum.OUTTIME.getCode());
            if (CollUtil.isNotEmpty(statusList)) {
                statusList = statusList.stream().filter(tempStatusList::contains).collect(Collectors.toList());
            } else {
                statusList = tempStatusList;
            }
            reqDto.setStatusList(statusList);
            reqDto.setStatus(null);
        }
        if (reqDto.getAuditType() == null) {
            reqDto.setNeAuditType(99);
        }

        PrescriptionSourceExtEnum.setSourceAndType(reqDto);
    }
}
