package com.xyy.saas.inquiry.common.api.rational.impl;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xyy.saas.inquiry.common.api.rational.InquiryDiagnosiscSearchApi;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryDiagnosiscDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryDiagnosiscDtoSearchParam;
import com.xyy.saas.inquiry.common.util.SwitchUtil;
import com.xyy.saas.inquiry.enums.rational.RemoteIndexEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.data.elasticsearch.core.IndexedObjectInformation;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.MatchAllQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TermQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TermsQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TermsQueryField;
import co.elastic.clients.elasticsearch._types.query_dsl.WildcardQuery;
import org.springframework.data.elasticsearch.core.query.UpdateResponse;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.Map;

@Slf4j
@Service
public class InquiryDiagnosiscSearchImpl implements InquiryDiagnosiscSearchApi<InquiryDiagnosiscDto> {

    @Resource
    private ElasticsearchOperations elasticsearchOperations;

    @Override
    public Boolean indexCreate() {
        try {
            // 获取索引操作接口
            IndexOperations indexOps = elasticsearchOperations.indexOps(IndexCoordinates.of(RemoteIndexEnum.YKQ_REMOTE_DIAGNOSISC.getIndexName()));
            
            // 检查索引是否已存在
            if (indexOps.exists()) {
                log.info("索引已存在: {}", RemoteIndexEnum.YKQ_REMOTE_DIAGNOSISC.getIndexName());
                return true;
            }
            
            // 设置索引的settings
            String settings = "{\"number_of_shards\":1,\"number_of_replicas\":1,\"refresh_interval\":\"1s\",\"max_result_window\":\"1000000\",\"analysis\":{\"analyzer\":{\"comma_analyzer\":{\"type\":\"custom\",\"tokenizer\":\"comma_tokenizer\"},\"diag_analyzer\":{\"type\":\"custom\",\"tokenizer\":\"diag_tokenizer\"}},\"tokenizer\":{\"comma_tokenizer\":{\"type\":\"pattern\",\"pattern\":\",\"},\"diag_tokenizer\":{\"type\":\"pattern\",\"pattern\":\"&\"}}}}";
            Document settingsDoc = Document.parse(settings);
            
            // 设置索引的mappings
            String mapping = "{\"properties\":{\"id \":{\"type\":\"integer\"},\"diagnosisPref\":{\"type\":\"keyword\"},\"caution\":{\"type\":\"integer\"},\"description\":{\"type\":\"keyword\"},\"diagnosisCodes\":{\"type\":\"text\",\"analyzer\":\"diag_analyzer\"},\"categories\":{\"type\":\"text\",\"analyzer\":\"comma_analyzer\"},\"commonProducts\":{\"type\":\"text\",\"analyzer\":\"comma_analyzer\"},\"status\":{\"type\":\"byte\"},\"yn\":{\"type\":\"byte\"},\"createUser\":{\"type\":\"keyword\"},\"updateUser\":{\"type\":\"keyword\"},\"createTime\":{\"format\":\"yyyy-MM-dd HH:mm:ss\",\"type\":\"date\"},\"updateTime\":{\"format\":\"yyyy-MM-dd HH:mm:ss\",\"type\":\"date\"}}}";
            Document mappingDoc = Document.parse(mapping);
            
            // 创建索引
            boolean created = indexOps.create(settingsDoc);
            if (created) {
                // 设置映射
                indexOps.putMapping(mappingDoc);
                log.info("索引创建成功: {}", RemoteIndexEnum.YKQ_REMOTE_DIAGNOSISC.getIndexName());
            }
            
            return created;
        } catch (Exception e) {
            log.error("创建索引失败", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<String, Object> getMapping() {
        return null;
    }

    @Override
    public Boolean indexDelete() {
        return null;
    }

    @Override
    public Boolean indexExists() {
        return null;
    }

    @Override
    public boolean add(InquiryDiagnosiscDto diagnosiscDto) {
        log.info("保存诊断禁忌的参数：{}", JSON.toJSONString(diagnosiscDto));
        try {
            IndexCoordinates indexCoordinates = IndexCoordinates.of(RemoteIndexEnum.YKQ_REMOTE_DIAGNOSISC.getIndexName());
            IndexQuery indexQuery = new IndexQuery();
            indexQuery.setId(diagnosiscDto.getId().toString());
            indexQuery.setObject(diagnosiscDto);
            
            String documentId = elasticsearchOperations.index(indexQuery, indexCoordinates);
            log.info("保存ES的结果：{}", documentId);
            return documentId != null && !documentId.isEmpty();
        } catch (Exception e) {
            log.error("添加文档失败", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean update(InquiryDiagnosiscDto diagnosiscDto) {
        log.info("修改诊断禁忌的参数：{}", JSON.toJSONString(diagnosiscDto));
        try {
            IndexCoordinates indexCoordinates = IndexCoordinates.of(RemoteIndexEnum.YKQ_REMOTE_DIAGNOSISC.getIndexName());
            // 将对象转换为Map
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> docMap = objectMapper.convertValue(diagnosiscDto, Map.class);
            UpdateQuery updateQuery = UpdateQuery.builder(diagnosiscDto.getId().toString())
                    .withDocument(Document.from(docMap))
                    .build();
            
            UpdateResponse response = elasticsearchOperations.update(updateQuery, indexCoordinates);
            log.info("修改ES的结果：{}", response.getResult());
            return response.getResult() == UpdateResponse.Result.UPDATED;
        } catch (Exception e) {
            log.error("更新文档失败", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean batchDel(List<Integer> ids) {
        log.info("删除诊断禁忌配置的参数：{}", JSON.toJSONString(ids));
        try {
            IndexCoordinates indexCoordinates = IndexCoordinates.of(RemoteIndexEnum.YKQ_REMOTE_DIAGNOSISC.getIndexName());
            
            for (Integer id : ids) {
                elasticsearchOperations.delete(id.toString(), indexCoordinates);
            }
            
            log.info("批量删除数据成功");
            return true;
        } catch (Exception e) {
            log.error("批量删除文档失败", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean batchInsert(List<InquiryDiagnosiscDto> dtoList) {
        log.info("批量新增诊断禁忌的参数：{}", JSON.toJSONString(dtoList));
        try {
            IndexCoordinates indexCoordinates = IndexCoordinates.of(RemoteIndexEnum.YKQ_REMOTE_DIAGNOSISC.getIndexName());
            
            List<IndexQuery> indexQueries = new ArrayList<>();
            for (InquiryDiagnosiscDto dto : dtoList) {
                IndexQuery indexQuery = new IndexQuery();
                indexQuery.setId(dto.getId().toString());
                indexQuery.setObject(dto);
                indexQueries.add(indexQuery);
            }
            
            List<IndexedObjectInformation> indexedObjects = elasticsearchOperations.bulkIndex(indexQueries, indexCoordinates);
            log.info("批量新增的结果：{}", indexedObjects.size());
            return indexedObjects != null && indexedObjects.size() == dtoList.size();
        } catch (Exception e) {
            log.error("批量插入文档失败", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public CommonResult<PageResult<InquiryDiagnosiscDto>> pageSearchInquiryDiagnosisc(InquiryDiagnosiscDtoSearchParam param) {
        try {
            // 参数验证
            if (param == null) {
                log.warn("搜索参数为空，返回空结果");
                PageResult<InquiryDiagnosiscDto> page = new PageResult<>();
                page.setTotal(0L);
                page.setList(new ArrayList<>());
                return CommonResult.success(page);
            }

            String indexName = RemoteIndexEnum.YKQ_REMOTE_DIAGNOSISC.getIndexName();
            IndexCoordinates indexCoordinates = IndexCoordinates.of(indexName);

            //添加默认分页信息
            if(param.getPageNo()<1 || param.getPageSize()<1){
                param.setPageNo(1);
                param.setPageSize(10000);
            }
            
            log.info("开始搜索诊断信息，索引: {}, 分页: {}/{}, 参数: {}", 
                    indexName, param.getPageNo(), param.getPageSize(), param);
            
            Pageable pageable = PageRequest.of(param.getPageNo() - 1, param.getPageSize(), Sort.by(Sort.Direction.DESC, "_id"));
            co.elastic.clients.elasticsearch._types.query_dsl.Query searchQuery = getSearchBuild(param);
            Query query = NativeQuery.builder()
                      .withQuery(searchQuery)
                      .withPageable(pageable)
                      .build();
            
            log.debug("构建的ES查询: {}", searchQuery);
            
            // 添加ES查询的详细错误处理
            SearchHits<InquiryDiagnosiscDto> searchHits;
            try {
                searchHits = elasticsearchOperations.search(query, InquiryDiagnosiscDto.class, indexCoordinates);
            } catch (Exception esException) {
                log.error("ES查询执行失败，详细错误: {}, 查询内容: {}", esException.getMessage(), searchQuery, esException);
                
                // 尝试使用简单的match_all查询作为fallback
                try {
                    log.info("尝试使用fallback查询(match_all)");
                    Query fallbackQuery = NativeQuery.builder()
                            .withQuery(MatchAllQuery.of(m -> m)._toQuery())
                            .withPageable(pageable)
                            .build();
                    searchHits = elasticsearchOperations.search(fallbackQuery, InquiryDiagnosiscDto.class, indexCoordinates);
                    log.info("Fallback查询成功，返回结果数: {}", searchHits.getTotalHits());
                } catch (Exception fallbackException) {
                    log.error("Fallback查询也失败: {}", fallbackException.getMessage(), fallbackException);
                    throw new RuntimeException("ES查询完全失败: " + esException.getMessage(), esException);
                }
            }
            
            if(searchHits.isEmpty()){
                log.info("搜索结果为空");
                PageResult<InquiryDiagnosiscDto> page = new PageResult<>();
                page.setTotal(0L);
                page.setList(new ArrayList<>());
                return CommonResult.success(page);
            }
            
            List<InquiryDiagnosiscDto> diagnosiscDtos = new ArrayList<>();
            searchHits.forEach(hit -> diagnosiscDtos.add(hit.getContent()));
            
            PageResult<InquiryDiagnosiscDto> page = new PageResult<>();
            page.setTotal(searchHits.getTotalHits());
            page.setList(diagnosiscDtos);
            
            log.info("搜索完成，返回结果数: {}, 总数: {}", page.getList().size(), page.getTotal());
            return CommonResult.success(page);
        } catch (Exception e) {
            log.error("分页搜索失败，索引: {}, 参数: {}, 错误信息: {}", 
                    RemoteIndexEnum.YKQ_REMOTE_DIAGNOSISC.getIndexName(), param, e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    public co.elastic.clients.elasticsearch._types.query_dsl.Query getSearchBuild(InquiryDiagnosiscDtoSearchParam param){
        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();
        boolean hasAnyCondition = false;
        
        if(!CollectionUtils.isEmpty(param.getDiagnosisCodes())){
            List<FieldValue> diagnosisCodeValues = param.getDiagnosisCodes().stream()
                    .map(FieldValue::of)
                    .collect(Collectors.toList());
            boolQueryBuilder.filter(TermsQuery.of(t -> t.field("diagnosisCodes").terms(TermsQueryField.of(tf -> tf.value(diagnosisCodeValues))))._toQuery());
            hasAnyCondition = true;
        }

        if(StringUtils.isNotBlank(param.getDiagnosisName())){
            boolQueryBuilder.filter(WildcardQuery.of(w -> w.field("diagnosisCodes").value("*".concat(SwitchUtil.replaceEsWildLikeStr(param.getDiagnosisName())).concat("*")))._toQuery());
            hasAnyCondition = true;
        }

        // 单独查类别
        if(!CollectionUtils.isEmpty(param.getCategorieNames())){
            List<FieldValue> categorieValues = param.getCategorieNames().stream()
                    .map(FieldValue::of)
                    .collect(Collectors.toList());
            boolQueryBuilder.filter(TermsQuery.of(t -> t.field("categories").terms(TermsQueryField.of(tf -> tf.value(categorieValues))))._toQuery());
            hasAnyCondition = true;
        }
        // 模糊查询名字
        if(CollectionUtils.isEmpty(param.getCategories()) && StringUtils.isNotBlank(param.getCommonName())){
            boolQueryBuilder.filter(WildcardQuery.of(w -> w.field("commonProducts").value("*".concat(SwitchUtil.replaceEsWildLikeStr(param.getCommonName())).concat("*")))._toQuery());
            hasAnyCondition = true;
        }

        if(param.getCaution() != null){
            boolQueryBuilder.must(TermQuery.of(t -> t.field("caution").value(param.getCaution()))._toQuery());
            hasAnyCondition = true;
        }
        if(param.getStatus() != null){
            boolQueryBuilder.must(TermQuery.of(t -> t.field("status").value(param.getStatus()))._toQuery());
            hasAnyCondition = true;
        }
        if(StringUtils.isNotBlank(param.getDiagnosisPref())){
            boolQueryBuilder.must(TermQuery.of(t -> t.field("diagnosisPref").value(param.getDiagnosisPref()))._toQuery());
            hasAnyCondition = true;
        }

        // 以下是or的关系
        BoolQuery.Builder nestedQueryBuilder = new BoolQuery.Builder();
        boolean hasNestedClauses = false;
        
        //名称 or 分类 匹配
        if(StringUtils.isNotBlank(param.getCommonName())){
            nestedQueryBuilder.should(WildcardQuery.of(w -> w.field("commonProducts").value("*".concat(SwitchUtil.replaceEsWildLikeStr(param.getCommonName())).concat("*")))._toQuery());
            hasNestedClauses = true;
        }
        if(!CollectionUtils.isEmpty(param.getCategories())){
            List<FieldValue> categoryValues = param.getCategories().stream()
                    .map(FieldValue::of)
                    .collect(Collectors.toList());
            nestedQueryBuilder.should(TermsQuery.of(t -> t.field("categories").terms(TermsQueryField.of(tf -> tf.value(categoryValues))))._toQuery());
            hasNestedClauses = true;
        }
        if(!CollectionUtils.isEmpty(param.getCommonProducts())){
            List<FieldValue> productValues = param.getCommonProducts().stream()
                    .map(FieldValue::of)
                    .collect(Collectors.toList());
            nestedQueryBuilder.should(TermsQuery.of(t -> t.field("commonProducts").terms(TermsQueryField.of(tf -> tf.value(productValues))))._toQuery());
            hasNestedClauses = true;
        }

        if(hasNestedClauses){
            boolQueryBuilder.should(nestedQueryBuilder.build()._toQuery());
            hasAnyCondition = true;
        }
        
        // 如果没有任何查询条件，返回match_all查询
        if(!hasAnyCondition){
            log.info("[DEBUG] 没有查询条件，使用match_all查询，参数: {}", param);
            return MatchAllQuery.of(m -> m)._toQuery();
        }
        
        log.info("[DEBUG] 构建bool查询，hasAnyCondition: {}, 参数: {}", hasAnyCondition, param);
        
        return boolQueryBuilder.build()._toQuery();
    }
}
