package com.xyy.saas.inquiry.common.controller.admin.migration.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.xyy.saas.inquiry.annotation.InquiryDateType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PrescriptionMigrationImInfoRespVo implements Serializable {

    @Schema(description = "发送方")
    private String sendName;

    @Schema(description = "聊天内容")
    private String sendContent;

    private LocalDateTime sendTime;

    @Schema(description = "发送时间")
    @InquiryDateType("sendTime")
    private String sendTimeStr;

}