package com.xyy.saas.inquiry.common.enums.migration;

import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationQueryVo;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Getter;

@Getter
public enum PrescriptionSourceExtEnum {

    LZINQUIRY(0, "灵芝问诊"), YKQ(1, "宜块钱"), SMARTFACE(2, "智慧脸"), SCANCODEINQUIRY(3, "扫码问诊"),
    NINHEALTH(4, "您健康"), UPLOAD(5, "上传处方"), SPEED(6, "极速问诊"), FAMOUS(7, "名医问诊");

    Integer value;
    String descript;

    PrescriptionSourceExtEnum(Integer value, String descript) {
        this.value = value;
        this.descript = descript;
    }

    /**
     * 处方类型：上传处方
     */
    private final static int UPLOAD_PRESCRIPTION = 3;
    private final static String UPLOAD_PRESCRIPTION_DESCRIPTION = "上传处方";

    private static Map<Integer, PrescriptionSourceExtEnum> prescriptionSourceEnumMap = Collections.unmodifiableMap(initPrescriptionSourceEnumMap());

    private static Map<Integer, PrescriptionSourceExtEnum> initPrescriptionSourceEnumMap() {
        return Arrays.asList(values()).stream().collect(Collectors.toMap(PrescriptionSourceExtEnum::getValue, ele -> ele));
    }

    public static boolean validValue(Integer value) {
        return value != null && prescriptionSourceEnumMap.containsKey(value);
    }

    public static void setSourceAndType(PrescriptionMigrationQueryVo prescriptionQueryVo) {

        Integer value = prescriptionQueryVo.getSourceExt();

        if (validValue(value)) {

            if (Objects.equals(UPLOAD.getValue(), value)) {
                prescriptionQueryVo.setType(UPLOAD_PRESCRIPTION);
                return;
            }
            PrescriptionSourceExtEnum prescriptionSourceExtEnum = prescriptionSourceEnumMap.get(value);
            if (prescriptionSourceExtEnum == null) {
                return;
            }
            if (prescriptionSourceExtEnum.equals(SPEED)) {

            } else if (prescriptionSourceExtEnum.equals(FAMOUS)) {

            } else {
                prescriptionQueryVo.setSource(prescriptionSourceExtEnum.getValue());
                prescriptionQueryVo.setNeqType(UPLOAD_PRESCRIPTION);
            }
        } else if (ThirdPlatformClientTypeEnum.getByCode(prescriptionQueryVo.getSourceExt()) != null) {
            prescriptionQueryVo.setSource(ThirdPlatformClientTypeEnum.getByCode(prescriptionQueryVo.getSourceExt()).getCode());
            prescriptionQueryVo.setNeqType(UPLOAD_PRESCRIPTION);
        }
    }
}
