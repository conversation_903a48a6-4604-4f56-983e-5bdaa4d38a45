package com.xyy.saas.inquiry.common.controller.admin.migration.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2019/09/11
 * @Describe
 */
@Data
@Slf4j
@ExcelIgnoreUnannotated
public class PrescriptionMigrationStoreExportVo implements Serializable {

    @ExcelProperty(value = "处方号")
    private String pref;

    @ExcelProperty(value = "患者姓名")
    private String patientName;

    @ExcelProperty(value = "年龄")
    private String patientAge;

    @ExcelProperty(value = "性别")
    private String patientSex;

    @ExcelProperty(value = "患者手机号")
    private String telephone;

    @ExcelProperty(value = "开方医生")
    private String physicianName;

    @ExcelProperty(value = "审方药师")
    private String pharmacistName;

    @ExcelProperty(value = "审方状态")
    private Integer auditStatus;

    @ExcelProperty(value = "处方状态")
    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "开方时间")
    private Date outPrescriptionTime;

    @ExcelProperty(value = "临床诊断")
    private String diagnosis;

    @ExcelProperty(value = "药品名称")
    private String productName;

    @ExcelProperty(value = "规格")
    private String attributeSpecification;

    @ExcelProperty(value = "数量")
    private String quantity;

    @ExcelProperty(value = "单次剂量")
    private String singleDose;

    @ExcelProperty(value = "计量单位")
    private String singleUnit;

    @ExcelProperty(value = "频次")
    private String useFrequency;

    @ExcelProperty(value = "用法")
    private String directions;

    @ExcelProperty(value = "副数(中药)")
    private String tcmTotal;


}