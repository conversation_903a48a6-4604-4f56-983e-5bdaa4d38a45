package com.xyy.saas.inquiry.common.service.rational.impl;

import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toList;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.web.core.util.LoginUserContextUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryRationalDictConfigDto;
import com.xyy.saas.inquiry.common.api.rational.dto.LimitDrugCatalogDto;
import com.xyy.saas.inquiry.common.api.rational.dto.LimitDrugCatalogSaveDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.LimitDrugCatalogOperationVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.SaasCategoryVo;
import com.xyy.saas.inquiry.common.convert.rational.RationalConvert;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryDrugCatalogLimitPo;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalDictConfig;
import com.xyy.saas.inquiry.common.dal.mysql.rational.InquiryDrugCatalogLimitMapper;
import com.xyy.saas.inquiry.common.dal.mysql.rational.InquiryRationalDictConfigMapper;
import com.xyy.saas.inquiry.common.enums.rational.LimitDrugCatalogNameTypeEnum;
import com.xyy.saas.inquiry.common.enums.rational.RationalDictType;
import com.xyy.saas.inquiry.common.service.productmid.InquiryMidCategoryApiImpl;
import com.xyy.saas.inquiry.common.service.rational.LimitDrugCatalogService;
import com.xyy.saas.inquiry.enums.rational.RationalStatusEnum;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 限制用药
 *
 * <AUTHOR>
 * @since 2024-04-25 14:40:39
 */
@Slf4j
@Service
public class LimitDrugCatalogServiceImpl implements LimitDrugCatalogService {

    @Resource
    private InquiryRationalDictConfigMapper inquiryRationalDictConfigMapper;

    @Resource
    private InquiryMidCategoryApiImpl inquiryMidCategoryApiImpl;

    @Resource
    private InquiryDrugCatalogLimitMapper inquiryDrugCatalogLimitMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean save(LimitDrugCatalogSaveDto limitDrugCatalogSaveDto) {

        if (CollectionUtils.isEmpty(limitDrugCatalogSaveDto.getHospitalPrefList())
                || CollectionUtils.isEmpty(limitDrugCatalogSaveDto.getProvinceCityAreaList())
                || CollectionUtils.isEmpty(limitDrugCatalogSaveDto.getCatalogIdList())) {
            log.info("InquiryDrugCatalogServiceImpl#save#error 方法入参param{}", JSON.toJSONString(limitDrugCatalogSaveDto));
            return false;
        }

        List<InquiryDrugCatalogLimitPo> inquiryDrugCatalogLimitPoList = new ArrayList<>();

        Date nowDate = new Date();

        for (String hospitalPref : limitDrugCatalogSaveDto.getHospitalPrefList()) {
            for (LimitDrugCatalogSaveDto.provinceCityAreaDto provinceCityArea : limitDrugCatalogSaveDto.getProvinceCityAreaList()) {
                for (Integer catalogId : limitDrugCatalogSaveDto.getCatalogIdList()) {
                    InquiryDrugCatalogLimitPo inquiryDrugCatalogLimitPo = InquiryDrugCatalogLimitPo.builder()
                        .hospitalPref(hospitalPref)
                            .province(provinceCityArea.getProvince())
                            .provinceCode(provinceCityArea.getProvinceCode())
                            .city(provinceCityArea.getCity())
                            .cityCode(provinceCityArea.getCityCode())
                            .area(provinceCityArea.getArea())
                            .areaCode(provinceCityArea.getAreaCode())
                            .catalogId(catalogId)
                            .limitType(limitDrugCatalogSaveDto.getLimitType())
                            .yn(CommonStatusEnum.ENABLE.getStatus())
                            .build();
                    inquiryDrugCatalogLimitPo.setCreateTime(LocalDateTime.now());
                    inquiryDrugCatalogLimitPo.setUpdateTime(LocalDateTime.now());
                    inquiryDrugCatalogLimitPo.setCreator(LoginUserContextUtils.getLoginUserId().toString());
                    inquiryDrugCatalogLimitPo.setUpdater(LoginUserContextUtils.getLoginUserId().toString());
                    inquiryDrugCatalogLimitPoList.add(inquiryDrugCatalogLimitPo);
                }
            }
        }

        for (List<InquiryDrugCatalogLimitPo> partitionData : Lists.partition(inquiryDrugCatalogLimitPoList, 500)) {
            inquiryDrugCatalogLimitMapper.insertByDuplicateKey(partitionData);
        }

        return true;
    }

    @Override
    public PageResult<LimitDrugCatalogDto> pageQuery(LimitDrugCatalogOperationVo limitDrugCatalogOperationDto) {

        if (limitDrugCatalogOperationDto.getCatalogId() != null && limitDrugCatalogOperationDto.getCatalogId() > 0) {

            Map<Integer, List<SaasCategoryVo>> integerListMap = inquiryMidCategoryApiImpl.queryCategoryChildPathByIds(Lists.newArrayList(limitDrugCatalogOperationDto.getCatalogId()));
            log.info("pageQuery queryCategoryChildPathByIds:{}", JSON.toJSONString(integerListMap));

            if (MapUtils.isEmpty(integerListMap)) {
                return new PageResult<>();
            }

            List<Integer> catalogIdList = integerListMap.values().stream().findFirst().orElse(Lists.newArrayList()).stream()
                    .filter(item -> item.getId() != null && item.getId() > 0)
                    .map(SaasCategoryVo::getId).distinct().collect(toList());

            if (CollectionUtils.isEmpty(catalogIdList)) {
                return new PageResult<>();
            }

            limitDrugCatalogOperationDto.setCatalogIdList(catalogIdList);
        }

        PageResult<InquiryDrugCatalogLimitPo> poPageInfo = inquiryDrugCatalogLimitMapper.queryByCondition(limitDrugCatalogOperationDto);

        if (CollectionUtils.isEmpty(poPageInfo.getList())) {
            return new PageResult<>();
        }

        PageResult<LimitDrugCatalogDto> resultPageInfo = RationalConvert.INSTANCE.convertLimitDrugCatalogPageInfo(poPageInfo);

        List<Integer> catalogIdList = new ArrayList<>();
        Set<Integer> createUserIdList = new HashSet<>();

        for (LimitDrugCatalogDto item : resultPageInfo.getList()) {
            if (item.getCatalogId() != null) {
                catalogIdList.add(item.getCatalogId());
            }
            if (StringUtils.isNotBlank(item.getCreateUser())) {
                createUserIdList.add(Integer.parseInt(item.getCreateUser()));
            }
        }

        // 父分类路径
        Map<Integer, List<SaasCategoryVo>> parentPathMap = inquiryMidCategoryApiImpl.queryCategoryParentPathByIds(catalogIdList);

        for (LimitDrugCatalogDto limitDrugCatalogDto : resultPageInfo.getList()) {
            // 填充分类路径
            limitDrugCatalogDto.setPathName(parentPathMap.getOrDefault(limitDrugCatalogDto.getCatalogId(), new ArrayList<>()).stream().map(SaasCategoryVo::getTitle).collect(joining(">")));
        }

        return resultPageInfo;
    }

    @Override
    public LimitDrugCatalogOperationVo getConfig() {

        InquiryRationalDictConfigDto inquiryRationalDictConfigDto = InquiryRationalDictConfigDto.builder()
                .type(RationalDictType.LIMIT_DRUG_CATALOG.getType())
                .name(LimitDrugCatalogNameTypeEnum.LIMIT_CATALOG_CONFIG.getName())
                .yn(CommonStatusEnum.ENABLE.getStatus()).build();

        List<InquiryRationalDictConfig> inquiryRationalDictConfigList = inquiryRationalDictConfigMapper.queryByCondition(inquiryRationalDictConfigDto);

        if (CollectionUtils.isEmpty(inquiryRationalDictConfigList)) {
            return LimitDrugCatalogOperationVo.builder().build();
        }

        return LimitDrugCatalogOperationVo.builder()
                .cautionLevel(inquiryRationalDictConfigList.get(0).getValue())
                .cautionDesc(inquiryRationalDictConfigList.get(0).getDescription()).build();
    }

    @Override
    public Boolean deleteByIdList(LimitDrugCatalogOperationVo limitDrugCatalogOperationDto) {

        return inquiryDrugCatalogLimitMapper.physicalDeleteByIdList(limitDrugCatalogOperationDto.getIdList()) > 0;
    }

    @Override
    public Boolean saveOrUpdateConfig(LimitDrugCatalogOperationVo limitDrugCatalogOperationDto) {

        InquiryRationalDictConfigDto inquiryRationalDictConfigDto = InquiryRationalDictConfigDto.builder()
                .type(RationalDictType.LIMIT_DRUG_CATALOG.getType())
                .names(Lists.newArrayList(LimitDrugCatalogNameTypeEnum.LIMIT_CATALOG_CONFIG.getName()))
                .yn(CommonStatusEnum.ENABLE.getStatus()).build();

        List<InquiryRationalDictConfig> inquiryRationalDictConfigList = inquiryRationalDictConfigMapper.queryByCondition(inquiryRationalDictConfigDto);

        if (CollectionUtils.isNotEmpty(inquiryRationalDictConfigList)) {

            InquiryRationalDictConfig inquiryRationalDictConfig = new InquiryRationalDictConfig();

            inquiryRationalDictConfig.setId(inquiryRationalDictConfigList.get(0).getId());
            inquiryRationalDictConfig.setValue(limitDrugCatalogOperationDto.getCautionLevel());
            inquiryRationalDictConfig.setDescription(limitDrugCatalogOperationDto.getCautionDesc());
            inquiryRationalDictConfig.setUpdater(LoginUserContextUtils.getLoginUserId().toString());
            inquiryRationalDictConfig.setUpdateTime(LocalDateTime.now());

            inquiryRationalDictConfigMapper.updateById(inquiryRationalDictConfig);

        } else {

            InquiryRationalDictConfig inquiryRationalDictConfig = new InquiryRationalDictConfig();

            inquiryRationalDictConfig.setType(RationalDictType.LIMIT_DRUG_CATALOG.getType());
            inquiryRationalDictConfig.setName(LimitDrugCatalogNameTypeEnum.LIMIT_CATALOG_CONFIG.getName());
            inquiryRationalDictConfig.setValue(limitDrugCatalogOperationDto.getCautionLevel());
            inquiryRationalDictConfig.setDescription(limitDrugCatalogOperationDto.getCautionDesc());
            inquiryRationalDictConfig.setStatus(RationalStatusEnum.ON.getCode());
            inquiryRationalDictConfig.setYn(CommonStatusEnum.ENABLE.getStatus());
            inquiryRationalDictConfig.setCreator(LoginUserContextUtils.getLoginUserId().toString());
            inquiryRationalDictConfig.setUpdater(LoginUserContextUtils.getLoginUserId().toString());
            inquiryRationalDictConfig.setCreateTime(LocalDateTime.now());
            inquiryRationalDictConfig.setUpdateTime(LocalDateTime.now());

            inquiryRationalDictConfigMapper.insert(inquiryRationalDictConfig);
        }

        return true;
    }
}
