package com.xyy.saas.inquiry.common.convert.migration;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationExportVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationInfoRespVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationRespVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationStoreExportVo;
import com.xyy.saas.inquiry.common.mq.message.migration.dto.MigrationPrescriptionEventDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationPrescriptionReqDto;
import com.xyy.saas.inquiry.pojo.migration.PrescriptionMigrationDetailDto;
import com.xyy.saas.inquiry.pojo.migration.PrescriptionMigrationInfoDto;
import com.xyy.saas.inquiry.pojo.migration.PrescriptionMigrationTcmDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
 * 迁移转换
 */
@Mapper
public interface MigrationConvert {

    MigrationConvert INSTANCE = Mappers.getMapper(MigrationConvert.class);

    MigrationPrescriptionEventDto convert(MigrationPrescriptionReqDto reqDto);

    MigrationPrescriptionReqDto convertDto(MigrationPrescriptionEventDto msg);

    @Mapping(target = "prescriptionDetailsList", ignore = true)
    @Mapping(target = "checkImageUrlList", ignore = true)
    PrescriptionMigrationInfoRespVo convertVo(PrescriptionMigrationInfoDto infoDto);

    PrescriptionMigrationExportVo convertExportVo(PrescriptionMigrationInfoDto p);

    /**
     * 填充中药信息到导出对象
     */
    default void fillTcmInfo(PrescriptionMigrationExportVo exportVo, String dosageAndUsage) {
        if (StrUtil.isNotBlank(dosageAndUsage)) {
            try {
                PrescriptionMigrationTcmDto tcm = JSONUtil.toBean(dosageAndUsage, PrescriptionMigrationTcmDto.class);
                if (tcm != null) {
                    exportVo.setTcmTotal(tcm.getTcmTotalDosage() != null ? tcm.getTcmTotalDosage().toString() : null);
                    exportVo.setTcmUsage(tcm.getTcmUsage());
                    exportVo.setTcmProcessingMethod(tcm.getTcmProcessingMethod());
                }
            } catch (Exception ignored) {
            }
        }
    }

    /**
     * 填充商品信息到导出对象
     */
    default void fillProductInfo(PrescriptionMigrationExportVo exportVo, PrescriptionMigrationDetailDto detail) {
        if (detail != null) {
            exportVo.setMedicinesName(detail.getMedicinesName());
            exportVo.setProductName(detail.getProductName());
            exportVo.setAttributeSpecification(detail.getAttributeSpecification());
            exportVo.setManufacturer(detail.getManufacturer());
            exportVo.setQuantity(detail.getQuantity() != null ? detail.getQuantity().toString() : null);
            exportVo.setSingleDose(detail.getSingleDose());
            exportVo.setSingleUnit(detail.getSingleUnit());
            exportVo.setUseFrequency(detail.getUseFrequency());
            exportVo.setDirections(detail.getDirections());
        }
    }

    List<PrescriptionMigrationStoreExportVo> convertStoreExcelVos(List<PrescriptionMigrationExportVo> resultList);

    List<PrescriptionMigrationRespVo> convertRespVo(List<PrescriptionMigrationInfoDto> list);
}
