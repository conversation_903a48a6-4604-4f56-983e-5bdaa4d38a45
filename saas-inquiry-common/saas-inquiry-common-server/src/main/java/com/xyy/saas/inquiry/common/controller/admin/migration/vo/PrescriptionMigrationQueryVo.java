package com.xyy.saas.inquiry.common.controller.admin.migration.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.xyy.saas.inquiry.constant.ValidateGroup.Add;
import com.xyy.saas.inquiry.constant.ValidateGroup.Query;
import com.xyy.saas.inquiry.constant.ValidateGroup.Update;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class PrescriptionMigrationQueryVo extends PageParam {

    /**
     * 年度
     */
    @NotEmpty(message = "数据年限不能为空", groups = {Update.class})
    private String year;

    /**
     * 机构号索引
     */
    private String organSign;

    /**
     * 处方ID
     */
    @NotNull(message = "ID不能为空", groups = Query.class)
    private Long id;

    /**
     * 处方ID集合
     */
    @NotEmpty(message = "所选处方不能为空", groups = Add.class)
    private List<Long> ids;


    /**
     * 处方号
     */
    private String pref;

    /**
     * 提交门店
     */
    private String drugstoreName;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 医师姓名
     */
    private String physicianName;

    /**
     * 开方时间
     */
    private LocalDateTime[] outPrescriptionTime;

    /**
     * 审批时间
     */
    private LocalDateTime[] approvalTime;

    /**
     * 审核药师
     */
    private String pharmacistName;

    /**
     * 审批状态
     */
    private Integer auditStatus;

    /**
     * 审批状态
     */
    private List<Integer> auditStatusList;

    /**
     * 审方类型 0 正常审方    1带方审方
     */
    private Integer auditType;

    private Integer neAuditType;


    /**
     * 问诊Guid
     */
    private String inquiryGuid;

    /**
     * 问诊状态
     */
    private String inquiryStatus;

    /**
     * 患者身份证号
     */
    private String idCard;

    /**
     * 患者手机号
     */
    private String telephone;

    /**
     * 处方类型：0视频处方，1图文处方 2 极速问诊
     */
    private Integer type;

    /**
     * 不等于的类型
     */
    private Integer neqType;
    /**
     * 处方来源：0灵芝问诊，1宜块钱，2 SaaS
     */
    private Integer source;

    // 处方状态 1 待开方 2 已关闭 3 已超时  4 开方成功、等待上传 5 上传成功 6 待签名 7 医师签名成功 8 医师签名失败 9 药师签名成功 10 药师签名失败
    private Integer status;

    /**
     * 处方状态列表
     */
    private List<Integer> statusList;

    /**
     * 问诊来源
     */
    private Integer inquirySource;

    /**
     * 药品类型 0 西药  1 中药
     */
    private Integer medicineType;

    private String inquiryNo;

    /**
     * 处方药品名称
     */
    private String productName;

    private Integer clientType;

    /**
     * 处方来源 后台
     */
    private Integer sourceAndClientType;

    /**
     * 商家 @see PrescriptionSourceExtEnum 处方来源、类型综合枚举：0灵芝问诊，1宜块钱 2 智慧脸  3  扫码问诊 4 您健康 5 上传处方
     */
    private Integer sourceExt;

}