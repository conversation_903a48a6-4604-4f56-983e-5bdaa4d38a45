package cn.iocoder.yudao.module.system.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * System 错误码枚举类
 * <p>
 * system 系统，使用 1-002-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== AUTH 模块 1-002-000-000 ==========
    ErrorCode AUTH_LOGIN_BAD_CREDENTIALS = new ErrorCode(1_002_000_000, "登录失败，账号密码不正确");
    ErrorCode AUTH_LOGIN_USER_DISABLED = new ErrorCode(1_002_000_001, "该用户账号已被禁用，无法登录！");
    ErrorCode AUTH_LOGIN_USER_LOCKED = new ErrorCode(1_002_000_002, "该账号尝试认证多次失败已被锁定，请等待{}分钟后重新认证或联系管理员帮助解锁！");
    ErrorCode AUTH_LOGIN_CAPTCHA_CODE_ERROR = new ErrorCode(1_002_000_004, "验证码不正确，原因：{}");
    ErrorCode AUTH_THIRD_LOGIN_NOT_BIND = new ErrorCode(1_002_000_005, "未绑定账号，需要进行绑定");
    ErrorCode AUTH_MOBILE_NOT_EXISTS = new ErrorCode(1_002_000_007, "手机号不存在");
    ErrorCode OA_LOGIN_PARAM_NOT_EXISTS = new ErrorCode(1_002_000_008, "OA账号登录异常,请联系管理员检查配置");
    ErrorCode OA_LOGIN_QUERY_USER_ERROR = new ErrorCode(1_002_000_009, "OA账号登录查询用户信息异常");
    ErrorCode OA_LOGIN_USER_INFO_ERROR = new ErrorCode(1_002_000_010, "OA账号用户信息异常,无法登录");
    ErrorCode API_LOGIN_CONFIG_ERROR = new ErrorCode(1_002_000_011, "三方应用授权密钥信息异常,无法登录");
    ErrorCode API_LOGIN_SIGN_ERROR = new ErrorCode(1_002_000_012, "三方应用授权签名异常,无法登录");

    ErrorCode AUTH_PWD_BAD_CREDENTIALS = new ErrorCode(1_002_000_013, "密码验证失败,请重试");
    ErrorCode ZHL_TICKET_LOGIN_ERROR = new ErrorCode(1_002_000_014, "智慧脸联合登录失败,请重新进入此页面");


    // ========== 菜单模块 1-002-001-000 ==========
    ErrorCode MENU_NAME_DUPLICATE = new ErrorCode(1_002_001_000, "已经存在该名字的菜单");
    ErrorCode MENU_PARENT_NOT_EXISTS = new ErrorCode(1_002_001_001, "父菜单不存在");
    ErrorCode MENU_PARENT_ERROR = new ErrorCode(1_002_001_002, "不能设置自己为父菜单");
    ErrorCode MENU_NOT_EXISTS = new ErrorCode(1_002_001_003, "菜单不存在");
    ErrorCode MENU_EXISTS_CHILDREN = new ErrorCode(1_002_001_004, "存在子菜单，无法删除");
    ErrorCode MENU_PARENT_NOT_DIR_OR_MENU = new ErrorCode(1_002_001_005, "父菜单的类型必须是目录或者菜单");

    // ========== 角色模块 1-002-002-000 ==========
    ErrorCode ROLE_NOT_EXISTS = new ErrorCode(1_002_002_000, "角色不存在");
    ErrorCode ROLE_NAME_DUPLICATE = new ErrorCode(1_002_002_001, "已经存在名为【{}】的角色");
    ErrorCode ROLE_CODE_DUPLICATE = new ErrorCode(1_002_002_002, "已经存在编码为【{}】的角色");
    ErrorCode ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE = new ErrorCode(1_002_002_003, "不能操作类型为系统内置的角色");
    ErrorCode ROLE_IS_DISABLE = new ErrorCode(1_002_002_004, "名字为【{}】的角色已被禁用");
    ErrorCode ROLE_ADMIN_CODE_ERROR = new ErrorCode(1_002_002_005, "编码【{}】不能使用");
    ErrorCode ROLE_CAN_NOT_ASSIGN_ROLE_MENU = new ErrorCode(1_002_002_003, "当前角色不可操作菜单权限");

    ErrorCode USER_ROLE_NO_ALLOW_ALLOCATION_ERROR = new ErrorCode(1_002_002_004, "不可操作用户[{}]角色");
    ErrorCode USER_ROLE_NO_ALLOW_EXISTS_ERROR = new ErrorCode(1_002_002_005, "[{}]角色不可和[{}]角色同时分配");

    ErrorCode USER_HAS_ROLE_NO_ALLOW_ERROR = new ErrorCode(1_002_002_006, "当前手机号用户已存在[{}]角色,请先解绑或更换其他手机号创建");

    // ========== 用户模块 1-002-003-000 ==========
    ErrorCode USER_USERNAME_EXISTS = new ErrorCode(1_002_003_000, "当前登录账号已经存在");
    ErrorCode USER_MOBILE_EXISTS = new ErrorCode(1_002_003_001, "该手机号已被其他用户绑定");
    ErrorCode USER_EMAIL_EXISTS = new ErrorCode(1_002_003_002, "邮箱已经存在");
    ErrorCode USER_NOT_EXISTS = new ErrorCode(1_002_003_003, "用户不存在,{}");
    ErrorCode USER_IMPORT_LIST_IS_EMPTY = new ErrorCode(1_002_003_004, "导入用户数据不能为空！");
    ErrorCode USER_PASSWORD_FAILED = new ErrorCode(1_002_003_005, "用户密码校验失败");
    ErrorCode USER_IS_DISABLE = new ErrorCode(1_002_003_006, "名字为【{}】的用户已被禁用");
    ErrorCode USER_IS_QUIT = new ErrorCode(1_002_003_007, "当前用户账号已注销，无法操作");
    ErrorCode USER_COUNT_MAX = new ErrorCode(1_002_003_008, "创建用户失败，原因：超过门店最大门店配额【{}】！");
    ErrorCode USER_IMPORT_INIT_PASSWORD = new ErrorCode(1_002_003_009, "初始密码不能为空");

    ErrorCode TENANT_USER_FINGER_PRINT_NOT_EXISTS = new ErrorCode(1_002_003_010, "门店员工指纹不存在");

    ErrorCode TENANT_USER_FINGER_PRINT_MORE_THAN = new ErrorCode(1_002_003_011, "门店员工指纹超过最大{}个,请删除后新增");

    ErrorCode TENANT_USER_FINGER_PRINT_CHECK_FAIL = new ErrorCode(1_002_003_012, "指纹验证失败,请重新验证");


    ErrorCode TENANT_USER_CLOCK_IN_LOG_NOT_EXISTS = new ErrorCode(1_002_003_013, "门店员工打卡记录不存在");


    ErrorCode CHANGE_MOBILE_FAIL_LOCKED = new ErrorCode(1_002_003_014, "该手机号尝试验证多次失败已被锁定，请等待{}分钟后重新验证或联系管理员帮助解锁！");
    ErrorCode TENANT_ADMIN_MOBILE_NOT_EXIST = new ErrorCode(1_002_003_015, "该用户手机号不存在,操作失败！");

    ErrorCode CHANGE_MOBILE_TIPS = new ErrorCode(1_002_003_016, "更换手机号提醒:{}");


    ErrorCode USER_SAVE_FACE_LIMIT_LOCKED = new ErrorCode(1_002_003_017, "当天操作次数已达上限{}次,用户已被锁定，请等待{}分钟后重试！");

    ErrorCode TENANT_BIND_OLD_HY_UN_EXISTS_FAIL = new ErrorCode(1_002_003_018, "该手机号在老版本荷叶系统中不存在，请检查后重新输入！");
    ErrorCode TENANT_BIND_OLD_HY_UN_STORE_FAIL = new ErrorCode(1_002_003_018, "该手机号在老版本荷叶系统中未查询到有效门店，请检查后重新输入！");
    ErrorCode TENANT_BIND_OLD_HY_MOBILE_HAS_BIND_FAIL = new ErrorCode(1_002_003_018, "该账号已被其他门店绑定，请确认！");

    ErrorCode TENANT_BIND_OLD_HY_HAS_NO_BIND_FAIL = new ErrorCode(1_002_003_018, "没有绑定老版本荷叶门店，无法操作");

    // ========== 部门模块 1-002-004-000 ==========
    ErrorCode DEPT_NAME_DUPLICATE = new ErrorCode(1_002_004_000, "已经存在该名字的部门");
    ErrorCode DEPT_PARENT_NOT_EXITS = new ErrorCode(1_002_004_001, "父级部门不存在");
    ErrorCode DEPT_NOT_FOUND = new ErrorCode(1_002_004_002, "当前部门不存在");
    ErrorCode DEPT_EXITS_CHILDREN = new ErrorCode(1_002_004_003, "存在子部门，无法删除");
    ErrorCode DEPT_PARENT_ERROR = new ErrorCode(1_002_004_004, "不能设置自己为父部门");
    ErrorCode DEPT_NOT_ENABLE = new ErrorCode(1_002_004_006, "部门【{}】不处于开启状态，不允许选择");
    ErrorCode DEPT_PARENT_IS_CHILD = new ErrorCode(1_002_004_007, "不能设置自己的子部门为父部门");

    // ========== 岗位模块 1-002-005-000 ==========
    ErrorCode POST_NOT_FOUND = new ErrorCode(1_002_005_000, "当前岗位不存在");
    ErrorCode POST_NOT_ENABLE = new ErrorCode(1_002_005_001, "岗位【{}】 不处于开启状态，不允许选择");
    ErrorCode POST_NAME_DUPLICATE = new ErrorCode(1_002_005_002, "已经存在该名字的岗位");
    ErrorCode POST_CODE_DUPLICATE = new ErrorCode(1_002_005_003, "已经存在该标识的岗位");

    // ========== 字典类型 1-002-006-000 ==========
    ErrorCode DICT_TYPE_NOT_EXISTS = new ErrorCode(1_002_006_001, "当前字典类型不存在");
    ErrorCode DICT_TYPE_NOT_ENABLE = new ErrorCode(1_002_006_002, "字典类型不处于开启状态，不允许选择");
    ErrorCode DICT_TYPE_NAME_DUPLICATE = new ErrorCode(1_002_006_003, "已经存在该名字的字典类型");
    ErrorCode DICT_TYPE_TYPE_DUPLICATE = new ErrorCode(1_002_006_004, "已经存在该类型的字典类型");
    ErrorCode DICT_TYPE_HAS_CHILDREN = new ErrorCode(1_002_006_005, "无法删除，该字典类型还有字典数据");

    // ========== 字典数据 1-002-007-000 ==========
    ErrorCode DICT_DATA_NOT_EXISTS = new ErrorCode(1_002_007_001, "当前字典数据不存在");
    ErrorCode DICT_DATA_NOT_ENABLE = new ErrorCode(1_002_007_002, "字典数据【{}】不处于开启状态，不允许选择");
    ErrorCode DICT_DATA_VALUE_DUPLICATE = new ErrorCode(1_002_007_003, "已经存在该值的字典数据");

    // ========== 通知公告 1-002-008-000 ==========
    ErrorCode NOTICE_NOT_FOUND = new ErrorCode(1_002_008_001, "当前通知公告不存在");

    // ========== 业务 1-002-009-000 ==========
    ErrorCode BIZ_NOT_EXISTS = new ErrorCode(1_002_009_001, "业务不存在");

    // ========== 短信渠道 1-002-011-000 ==========
    ErrorCode SMS_CHANNEL_NOT_EXISTS = new ErrorCode(1_002_011_000, "短信渠道不存在");
    ErrorCode SMS_CHANNEL_DISABLE = new ErrorCode(1_002_011_001, "短信渠道不处于开启状态，不允许选择");
    ErrorCode SMS_CHANNEL_HAS_CHILDREN = new ErrorCode(1_002_011_002, "无法删除，该短信渠道还有短信模板");

    // ========== 短信模板 1-002-012-000 ==========
    ErrorCode SMS_TEMPLATE_NOT_EXISTS = new ErrorCode(1_002_012_000, "短信模板不存在");
    ErrorCode SMS_TEMPLATE_CODE_DUPLICATE = new ErrorCode(1_002_012_001, "已经存在编码为【{}】的短信模板");
    ErrorCode SMS_TEMPLATE_API_ERROR = new ErrorCode(1_002_012_002, "短信 API 模板调用失败，原因是：{}");
    ErrorCode SMS_TEMPLATE_API_AUDIT_CHECKING = new ErrorCode(1_002_012_003, "短信 API 模版无法使用，原因：审批中");
    ErrorCode SMS_TEMPLATE_API_AUDIT_FAIL = new ErrorCode(1_002_012_004, "短信 API 模版无法使用，原因：审批不通过，{}");
    ErrorCode SMS_TEMPLATE_API_NOT_FOUND = new ErrorCode(1_002_012_005, "短信 API 模版无法使用，原因：模版不存在");

    // ========== 短信发送 1-002-013-000 ==========
    ErrorCode SMS_SEND_MOBILE_NOT_EXISTS = new ErrorCode(1_002_013_000, "手机号不存在");
    ErrorCode SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS = new ErrorCode(1_002_013_001, "模板参数【{}】缺失");
    ErrorCode SMS_SEND_TEMPLATE_NOT_EXISTS = new ErrorCode(1_002_013_002, "短信模板不存在");

    // ========== 短信验证码 1-002-014-000 ==========
    ErrorCode SMS_CODE_NOT_FOUND = new ErrorCode(1_002_014_000, "验证码错误,请检查后重新输入");
    ErrorCode SMS_CODE_EXPIRED = new ErrorCode(1_002_014_001, "验证码已过期");
    ErrorCode SMS_CODE_USED = new ErrorCode(1_002_014_002, "验证码已使用");
    ErrorCode SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY = new ErrorCode(1_002_014_004, "超过每日短信发送数量");
    ErrorCode SMS_CODE_SEND_TOO_FAST = new ErrorCode(1_002_014_005, "短信发送过于频繁");

    // ========== 门店信息 1-002-015-000 ==========
    ErrorCode TENANT_NOT_EXISTS = new ErrorCode(1_002_015_000, "门店不存在");
    ErrorCode TENANT_DISABLE = new ErrorCode(1_002_015_001, "名字为【{}】的门店已被禁用");
    ErrorCode TENANT_EXPIRE = new ErrorCode(1_002_015_002, "名字为【{}】的门店已过期");
    ErrorCode TENANT_CAN_NOT_UPDATE_SYSTEM = new ErrorCode(1_002_015_003, "系统门店不能进行修改、删除等操作！");
    ErrorCode TENANT_NAME_DUPLICATE = new ErrorCode(1_002_015_004, "名字为【{}】的门店已存在");
    ErrorCode TENANT_TYPE_UN_SUPPORT = new ErrorCode(1_002_015_005, "门店类型暂不支持");
    ErrorCode TENANT_TYPE_MUST_RELATION_CHAIN = new ErrorCode(1_002_015_006, "{}必须关联总部");
    ErrorCode TENANT_TYPE_CAN_NOT_RELATION_CHAIN = new ErrorCode(1_002_015_007, "{}不能关联总部");
    ErrorCode TENANT_TYPE_CHAIN_STORE_CAN_NOT_DELETE = new ErrorCode(1_002_015_007, "总部还关联有门店，无法删除！");

    ErrorCode TENANT_BUSINESS_LICENSE_NUMBER_DUPLICATE = new ErrorCode(1_002_015_08, "营业执照号为【{}】的门店已存在");
    ErrorCode TENANT_BUSINESS_LICENSE_NAME_DUPLICATE = new ErrorCode(1_002_015_08, "营业执照名称为【{}】的门店已存在");
    ErrorCode TENANT_WEBSITE_DUPLICATE = new ErrorCode(1_002_015_005, "域名为【{}】的门店已存在");
    ErrorCode TENANT_USER_RELATION_NOT_EXISTS = new ErrorCode(1_002_015_006, "该用户账号门店绑定关系不存在！");
    ErrorCode TENANT_USER_RELATION_NOT_EXISTS_LOGIN = new ErrorCode(1_002_015_006, "该用户账号不存在门店绑定关系,无法登录！");
    ErrorCode TENANT_CERTIFICATE_NOT_EXISTS = new ErrorCode(1_002_015_007, "门店资质证件信息不存在");
    ErrorCode TENANT_ADMIN_NOT_OPERATE = new ErrorCode(1_002_015_008, "门店管理员不可操作变更 {}");
    ErrorCode TENANT_RELATION_BIND_NEED_ONE = new ErrorCode(1_002_015_009, "用户需保留一个门店绑定关系");
    ErrorCode TENANT_ADMIN_NOT_LOGOFF = new ErrorCode(1_002_015_010, "当前用户为门店管理员，请联系官方客服进行账号注销操作");

    ErrorCode TENANT_HEAD_TYPE_CHANGE_ERROR = new ErrorCode(1_002_015_011, "总部类型不能修改");
    ErrorCode TENANT_HEAD_STORE_TYPE_CHANGE_ERROR = new ErrorCode(1_002_015_012, "连锁门店不可变更为总部类型");
    ErrorCode TENANT_HEAD_DISABLE_ERROR = new ErrorCode(1_002_015_013, "当前总部下存在绑定门店,不可禁用");

    ErrorCode TENANT_BIZ_NOT_EXISTS = new ErrorCode(1_002_015_014, "当前门店未开通业务线套餐，请联系管理员");


    // ========== 门店套餐 1-002-016-000 ==========
    ErrorCode TENANT_PACKAGE_NOT_EXISTS = new ErrorCode(1_002_016_000, "门店套餐不存在");
    ErrorCode TENANT_PACKAGE_USED = new ErrorCode(1_002_016_001, "存在门店开通该套餐，无法删除");
    ErrorCode TENANT_PACKAGE_DISABLE = new ErrorCode(1_002_016_002, "名字为【{}】的门店套餐已被禁用");
    ErrorCode TENANT_PACKAGE_NAME_DUPLICATE = new ErrorCode(1_002_016_003, "名字为【{}】的套餐已存在");
    ErrorCode TENANT_PACKAGE_MENU_ERROR = new ErrorCode(1_002_016_004, "名字为【{}】的套餐菜单设置错误");
    ErrorCode TENANT_PACKAGE_RELATION_NOT_EXISTS = new ErrorCode(1_002_016_100, "门店套餐订单不存在");
    ErrorCode TENANT_PACKAGE_RELATION_STATUS_ERROR = new ErrorCode(1_002_016_101, "门店套餐包状态为【{}】,操作失败");
    ErrorCode TENANT_PACKAGE_RELATION_EXISTS_STATUS_ERROR = new ErrorCode(1_002_016_102, "存在状态为退款或作废的套餐包,操作失败");

    ErrorCode TENANT_PACKAGE_RELATION_BIZ_TYPE_ERROR = new ErrorCode(1_002_016_103, "仅可选择业务类型的套餐进行批量切换操作");

    ErrorCode TENANT_PACKAGE_SHARE_RELATION_NOT_EXISTS = new ErrorCode(1_002_016_104, "总部门店套餐共享关系不存在");

    ErrorCode TENANT_PACKAGE_SHARE_RELATION_TENANT_TYPE_FAIL = new ErrorCode(1_002_016_105, "{} 门店类型非连锁,不可套餐共享");
    ErrorCode TENANT_PACKAGE_RELATION_CHANGE_FAIL = new ErrorCode(1_002_016_106, "自定义套餐或退款作废套餐,不操作更换");

    ErrorCode TENANT_PACKAGE_ITEM_FAIL = new ErrorCode(1_002_016_007, "问诊方式不能为空");

    ErrorCode TENANT_PACKAGE_PRESCRIPTION_VALUE_DUPLICATE = new ErrorCode(1_002_016_007, "套餐处方类型不可重复或存在为空");
    ErrorCode TENANT_PACKAGE_RELATION_EXISTS = new ErrorCode(1_002_016_106, "门店套餐订单已存在");
    ErrorCode TENANT_BIZ_RELATION_NOT_EXISTS = new ErrorCode(1_002_016_107, "门店业务关系不存在");
    ErrorCode TENANT_BIZ_RELATION_EXISTS = new ErrorCode(1_002_016_108, "智慧脸已有已生效和待生效的套餐，请先变更原套餐状态后再添加");


    // ========== 社交用户 1-002-018-000 ==========
    ErrorCode SOCIAL_USER_AUTH_FAILURE = new ErrorCode(1_002_018_000, "社交授权失败，原因是：{}");
    ErrorCode SOCIAL_USER_NOT_FOUND = new ErrorCode(1_002_018_001, "社交授权失败，找不到对应的用户");

    ErrorCode SOCIAL_CLIENT_WEIXIN_MINI_APP_PHONE_CODE_ERROR = new ErrorCode(1_002_018_200, "获得手机号失败");
    ErrorCode SOCIAL_CLIENT_WEIXIN_MINI_APP_QRCODE_ERROR = new ErrorCode(1_002_018_201, "获得小程序码失败");
    ErrorCode SOCIAL_CLIENT_NOT_EXISTS = new ErrorCode(1_002_018_202, "社交客户端不存在");
    ErrorCode SOCIAL_CLIENT_UNIQUE = new ErrorCode(1_002_018_203, "社交客户端已存在配置");
    ErrorCode SOCIAL_CLIENT_WEIXIN_MINI_APP_SUBSCRIBE_TEMPLATE_ERROR = new ErrorCode(1_002_018_204, "获得小程序订阅消息模版失败");
    ErrorCode SOCIAL_CLIENT_WEIXIN_MINI_APP_SUBSCRIBE_MESSAGE_ERROR = new ErrorCode(1_002_018_205, "发送小程序订阅消息失败");

    // ========== OAuth2 客户端 1-002-020-000 =========
    ErrorCode OAUTH2_CLIENT_NOT_EXISTS = new ErrorCode(1_002_020_000, "OAuth2 客户端不存在");
    ErrorCode OAUTH2_CLIENT_EXISTS = new ErrorCode(1_002_020_001, "OAuth2 客户端编号已存在");
    ErrorCode OAUTH2_CLIENT_DISABLE = new ErrorCode(1_002_020_002, "OAuth2 客户端已禁用");
    ErrorCode OAUTH2_CLIENT_AUTHORIZED_GRANT_TYPE_NOT_EXISTS = new ErrorCode(1_002_020_003, "不支持该授权类型");
    ErrorCode OAUTH2_CLIENT_SCOPE_OVER = new ErrorCode(1_002_020_004, "授权范围过大");
    ErrorCode OAUTH2_CLIENT_REDIRECT_URI_NOT_MATCH = new ErrorCode(1_002_020_005, "无效 redirect_uri: {}");
    ErrorCode OAUTH2_CLIENT_CLIENT_SECRET_ERROR = new ErrorCode(1_002_020_006, "无效 client_secret: {}");

    // ========== OAuth2 授权 1-002-021-000 =========
    ErrorCode OAUTH2_GRANT_CLIENT_ID_MISMATCH = new ErrorCode(1_002_021_000, "client_id 不匹配");
    ErrorCode OAUTH2_GRANT_REDIRECT_URI_MISMATCH = new ErrorCode(1_002_021_001, "redirect_uri 不匹配");
    ErrorCode OAUTH2_GRANT_STATE_MISMATCH = new ErrorCode(1_002_021_002, "state 不匹配");

    // ========== OAuth2 授权 1-002-022-000 =========
    ErrorCode OAUTH2_CODE_NOT_EXISTS = new ErrorCode(1_002_022_000, "code 不存在");
    ErrorCode OAUTH2_CODE_EXPIRE = new ErrorCode(1_002_022_001, "code 已过期");

    // ========== 邮箱账号 1-002-023-000 ==========
    ErrorCode MAIL_ACCOUNT_NOT_EXISTS = new ErrorCode(1_002_023_000, "邮箱账号不存在");
    ErrorCode MAIL_ACCOUNT_RELATE_TEMPLATE_EXISTS = new ErrorCode(1_002_023_001, "无法删除，该邮箱账号还有邮件模板");

    // ========== 邮件模版 1-002-024-000 ==========
    ErrorCode MAIL_TEMPLATE_NOT_EXISTS = new ErrorCode(1_002_024_000, "邮件模版不存在");
    ErrorCode MAIL_TEMPLATE_CODE_EXISTS = new ErrorCode(1_002_024_001, "邮件模版 code【{}】 已存在");

    // ========== 邮件发送 1-002-025-000 ==========
    ErrorCode MAIL_SEND_TEMPLATE_PARAM_MISS = new ErrorCode(1_002_025_000, "模板参数【{}】缺失");
    ErrorCode MAIL_SEND_MAIL_NOT_EXISTS = new ErrorCode(1_002_025_001, "邮箱不存在");

    // ========== 站内信模版 1-002-026-000 ==========
    ErrorCode NOTIFY_TEMPLATE_NOT_EXISTS = new ErrorCode(1_002_026_000, "站内信模版不存在");
    ErrorCode NOTIFY_TEMPLATE_CODE_DUPLICATE = new ErrorCode(1_002_026_001, "已经存在编码为【{}】的站内信模板");

    // ========== 站内信模版 1-002-027-000 ==========

    // ========== 站内信发送 1-002-028-000 ==========
    ErrorCode NOTIFY_SEND_TEMPLATE_PARAM_MISS = new ErrorCode(1_002_028_000, "模板参数【{}】缺失");

    // ========== OA 1-002-029-000 ==========
    ErrorCode OA_WHITE_LIST_NOT_EXISTS = new ErrorCode(1_002_029_000, "OA-用户白名单不存在,请联系管理员");
    ErrorCode OA_WHITE_LIST_EXISTS = new ErrorCode(1_002_029_001, "OA-用户【{}】已存在");


    // ========== app版本管理 1-002-030-000 ==========
    ErrorCode APP_VERSION_NOT_EXISTS = new ErrorCode(1_002_030_000, "app版本不存在");
    // 灰度升级时必须选择灰度租户
    ErrorCode APP_VERSION_GRAY_TENANT_NOT_EXISTS = new ErrorCode(1_002_030_001, "灰度升级时必须选择灰度租户");
    // 内部版本号小于当前应用最新版本号，无法实现版本升级
    ErrorCode APP_VERSION_INNER_VERSION_ERROR = new ErrorCode(1_002_030_002, "当前版本编码小于等于此应用最新版本编码，请调整");

    // ========== 门店服务包 1-002-040-000 ==========
    ErrorCode TENANT_TRANSMISSION_SERVICE_PACK_RELATION_NOT_EXISTS = new ErrorCode(1_002_040_000, "门店-开通服务包关系不存在");

    ErrorCode TENANT_TRANSMISSION_SERVICE_PACK_INFO_NOT_EXISTS = new ErrorCode(1_002_040_001, "【{}】:【{}】信息不存在");

    ErrorCode TENANT_TRANSMISSION_SERVICE_PACK_IDS_NOT_EXISTS = new ErrorCode(1_002_040_002, "服务包不可为空");
    ErrorCode TENANT_TRANSMISSION_SERVICE_PACK_NOT_REPEAT = new ErrorCode(1_002_040_002, "{} 不可重复");

    ErrorCode TENANT_TRANSMISSION_SERVICE_CATALOG_IDS_NOT_EXISTS = new ErrorCode(1_002_040_002, "目录不可为空");
    ErrorCode TENANT_TRANSMISSION_SERVICE_CATALOG_DISABLE = new ErrorCode(1_002_040_002, "不能选择禁用的目录版本");

    ErrorCode TENANT_TRANSMISSION_SERVICE_PROVINCE_NO_SAME = new ErrorCode(1_002_040_003, "请选择同一省的门店后再进行切换操作哦~");

    ErrorCode TENANT_TRANSMISSION_SERVICE_CATALOG_PROVINCE_NO_SAME = new ErrorCode(1_002_040_004, "目录所属地区和门店地区不一致,不可操作切换~");

    // ========== 三方应用配置 1-002-050-000 ==========
    ErrorCode TENANT_THIRD_APP_NOT_EXISTS = new ErrorCode(1_002_050_000, "三方应用配置不存在");
    ErrorCode TENANT_THIRD_APP_KEY_DUPLICATE = new ErrorCode(1_002_050_001, "已经存在相同 appKey 的三方应用配置");

    ErrorCode TENANT_TRANSMISSION_ORGAN_NOT_EXISTS = new ErrorCode(1_002_051_000, "ERP对接机构不存在");
    ErrorCode TENANT_TRANSMISSION_ORGAN_REPETITION = new ErrorCode(1_002_051_001, "ERP对接机构重复添加");


    ErrorCode USER_FACE_NOT_EXISTS = new ErrorCode(1_002_060_001, "人脸信息不存在,请至荷叶问诊App-设置-审方人脸识别 录入人脸信息");

    ErrorCode USER_FACE_VERIFY_FAIL = new ErrorCode(1_002_060_002, "验证失败,请检查重新录入人脸信息");
}
