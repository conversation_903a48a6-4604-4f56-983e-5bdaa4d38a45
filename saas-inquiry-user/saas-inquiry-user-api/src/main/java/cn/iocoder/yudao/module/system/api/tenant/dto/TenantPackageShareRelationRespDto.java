package cn.iocoder.yudao.module.system.api.tenant.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "总部门店套餐共享 Response Dto")
@Data
public class TenantPackageShareRelationRespDto implements Serializable {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "12924")
    private Long id;

    @Schema(description = "总部id", example = "9866")
    private Long headTenantId;

    private Long tenantId;

    private String tenantName;

    private String tenantPref;

    @Schema(description = "业务类型 0-问诊,1-智慧脸...", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer bizType;

    @Schema(description = "共享套餐开通关系表id", requiredMode = Schema.RequiredMode.REQUIRED, example = "574")
    private Long tenantPackageId;

    @Schema(description = "扩展信息")
    private String ext;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;


    /**
     * 联系人
     */
    private String contactName;
    /**
     * 联系手机
     */
    private String contactMobile;

    /**
     * 省
     */
    private String province;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市
     */
    private String city;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 区
     */
    private String area;
    /**
     * 区编码
     */
    private String areaCode;
    /**
     * 药店地址
     */
    private String address;

}