package com.xyy.saas.inquiry.product.server.service.productcategory;

import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategoryListReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategorySaveReqVO;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import jakarta.annotation.Resource;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;

import com.xyy.saas.inquiry.product.server.dal.dataobject.category.ProductCategoryDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.productcategory.ProductCategoryMapper;

import org.springframework.context.annotation.Import;
import java.util.*;

import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.*;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.*;
import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.*;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.*;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_CATEGORY_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link ProductCategoryServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(ProductCategoryServiceImpl.class)
public class ProductCategoryServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ProductCategoryServiceImpl productCategoryService;

    @Resource
    private ProductCategoryMapper productCategoryMapper;

    @Test
    public void testCreateProductCategory_success() {
        // 准备参数
        ProductCategorySaveReqVO createReqVO = randomPojo(ProductCategorySaveReqVO.class).setId(null);

        // 调用
        Long productCategoryId = productCategoryService.createProductCategory(createReqVO);
        // 断言
        assertNotNull(productCategoryId);
        // 校验记录的属性是否正确
        ProductCategoryDO productCategory = productCategoryMapper.selectById(productCategoryId);
        assertPojoEquals(createReqVO, productCategory, "id");
    }

    @Test
    public void testUpdateProductCategory_success() {
        // mock 数据
        ProductCategoryDO dbProductCategory = randomPojo(ProductCategoryDO.class);
        productCategoryMapper.insert(dbProductCategory);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ProductCategorySaveReqVO updateReqVO = randomPojo(ProductCategorySaveReqVO.class, o -> {
            o.setId(dbProductCategory.getId()); // 设置更新的 ID
        });

        // 调用
        productCategoryService.updateProductCategory(updateReqVO);
        // 校验是否更新正确
        ProductCategoryDO productCategory = productCategoryMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, productCategory);
    }

    @Test
    public void testUpdateProductCategory_notExists() {
        // 准备参数
        ProductCategorySaveReqVO updateReqVO = randomPojo(ProductCategorySaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> productCategoryService.updateProductCategory(updateReqVO), PRODUCT_CATEGORY_NOT_EXISTS);
    }

    @Test
    public void testDeleteProductCategory_success() {
        // mock 数据
        ProductCategoryDO dbProductCategory = randomPojo(ProductCategoryDO.class);
        productCategoryMapper.insert(dbProductCategory);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbProductCategory.getId();

        // 调用
        productCategoryService.deleteProductCategory(id);
       // 校验数据不存在了
       assertNull(productCategoryMapper.selectById(id));
    }

    @Test
    public void testDeleteProductCategory_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> productCategoryService.deleteProductCategory(id), PRODUCT_CATEGORY_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetProductCategoryList() {
       // mock 数据
       ProductCategoryDO dbProductCategory = randomPojo(ProductCategoryDO.class, o -> { // 等会查询到
           o.setName(null);
           o.setDictId(null);
           o.setParentDictId(null);
           o.setSortOrder(null);
           o.setRemark(null);
           o.setCreateTime(null);
       });
       productCategoryMapper.insert(dbProductCategory);
       // 测试 name 不匹配
       productCategoryMapper.insert(cloneIgnoreId(dbProductCategory, o -> o.setName(null)));
       // 测试 dictId 不匹配
       productCategoryMapper.insert(cloneIgnoreId(dbProductCategory, o -> o.setDictId(null)));
       // 测试 parentDictId 不匹配
       productCategoryMapper.insert(cloneIgnoreId(dbProductCategory, o -> o.setParentDictId(null)));
       // 测试 sortOrder 不匹配
       productCategoryMapper.insert(cloneIgnoreId(dbProductCategory, o -> o.setSortOrder(null)));
       // 测试 remark 不匹配
       productCategoryMapper.insert(cloneIgnoreId(dbProductCategory, o -> o.setRemark(null)));
       // 测试 createTime 不匹配
       productCategoryMapper.insert(cloneIgnoreId(dbProductCategory, o -> o.setCreateTime(null)));
       // 准备参数
       ProductCategoryListReqVO reqVO = new ProductCategoryListReqVO();
       reqVO.setName(null);
       reqVO.setDictId(null);
       reqVO.setParentDictId(null);
       reqVO.setSortOrder(null);
       reqVO.setRemark(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<ProductCategoryDO> list = productCategoryService.getProductCategoryList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbProductCategory, list.get(0));
    }

}