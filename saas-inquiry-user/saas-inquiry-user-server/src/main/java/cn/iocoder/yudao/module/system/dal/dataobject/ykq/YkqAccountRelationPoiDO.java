package cn.iocoder.yudao.module.system.dal.dataobject.ykq;

import lombok.*;
import java.io.Serializable;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 账号门店对应关系 DO
 *
 * <AUTHOR>
 */
@TableName("ykq_account_relation_poi")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YkqAccountRelationPoiDO implements Serializable {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 账号ID
     */
    private String accountId;
    /**
     * B端药店ID
     */
    private String poiId;
    /**
     * 绑定人ID商家自行绑定是account_id或父账号account_id；运营绑定是员工OA邮箱
     */
    private String cid;
    /**
     * 0有效1无效
     */
    private Integer status;

}