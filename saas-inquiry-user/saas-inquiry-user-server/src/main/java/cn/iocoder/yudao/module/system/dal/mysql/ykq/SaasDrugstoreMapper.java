package cn.iocoder.yudao.module.system.dal.mysql.ykq;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.dal.dataobject.ykq.SaasDrugstoreDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 药店机构 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SaasDrugstoreMapper extends BaseMapperX<SaasDrugstoreDO> {

    default List<SaasDrugstoreDO> selectListByOrganSigns(List<String> organSigns) {
        return selectList(new LambdaQueryWrapperX<SaasDrugstoreDO>()
            .in(SaasDrugstoreDO::getOrganSign, organSigns)
            .eq(SaasDrugstoreDO::getStatus, 1));
    }

    default SaasDrugstoreDO selectOneByOrganSign(String organSign) {
        return selectOne(new LambdaQueryWrapperX<SaasDrugstoreDO>()
            .in(SaasDrugstoreDO::getOrganSign, organSign)
            .eq(SaasDrugstoreDO::getStatus, 1));
    }

}