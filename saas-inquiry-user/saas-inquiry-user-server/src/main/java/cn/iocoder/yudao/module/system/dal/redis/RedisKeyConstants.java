package cn.iocoder.yudao.module.system.dal.redis;

import cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;

/**
 * System Redis Key 枚举类
 *
 * <AUTHOR>
 */
public interface RedisKeyConstants {

    /**
     * 当前系统默认配置的redis缓存key
     */
    String SYSTEM_DEFAULT_CONFIG = "system_default_config";

    /**
     * 指定部门的所有子部门编号数组的缓存
     * <p>
     * KEY 格式：dept_children_ids:{id} VALUE 数据类型：String 子部门编号集合
     */
    String DEPT_CHILDREN_ID_LIST = "dept_children_ids";

    /**
     * 角色的缓存
     * <p>
     * KEY 格式：role:{id} VALUE 数据类型：String 角色信息
     */
    String ROLE = "role";

    /**
     * 用户拥有的角色编号的缓存
     * <p>
     * KEY 格式：user_role_ids:{userId}:{tenantId} VALUE 数据类型：String 角色编号集合
     */
    String USER_ROLE_ID_LIST = "user_role_ids";

    /**
     * 拥有指定菜单的角色编号的缓存
     * <p>
     * KEY 格式：user_role_ids:{menuId}:{tenantId} VALUE 数据类型：String 角色编号集合
     */
    String MENU_ROLE_ID_LIST = "menu_role_ids";

    /**
     * 拥有权限对应的菜单编号数组的缓存
     * <p>
     * KEY 格式：permission_menu_ids:{permission} VALUE 数据类型：String 菜单编号数组
     */
    String PERMISSION_MENU_ID_LIST = "permission_menu_ids";

    /**
     * OAuth2 客户端的缓存
     * <p>
     * KEY 格式：oauth_client:{id} VALUE 数据类型：String 客户端信息
     */
    String OAUTH_CLIENT = "oauth_client";

    /**
     * 访问令牌的缓存
     * <p>
     * KEY 格式：oauth2_access_token:{token} VALUE 数据类型：String 访问令牌信息 {@link OAuth2AccessTokenDO}
     * <p>
     * 由于动态过期时间，使用 RedisTemplate 操作
     */
    String OAUTH2_ACCESS_TOKEN = "oauth2_access_token:%s";

    /**
     * 站内信模版的缓存
     * <p>
     * KEY 格式：notify_template:{code} VALUE 数据格式：String 模版信息
     */
    String NOTIFY_TEMPLATE = "notify_template";

    /**
     * 邮件账号的缓存
     * <p>
     * KEY 格式：mail_account:{id} VALUE 数据格式：String 账号信息
     */
    String MAIL_ACCOUNT = "mail_account";

    /**
     * 邮件模版的缓存
     * <p>
     * KEY 格式：mail_template:{code} VALUE 数据格式：String 模版信息
     */
    String MAIL_TEMPLATE = "mail_template";

    /**
     * 短信模版的缓存
     * <p>
     * KEY 格式：sms_template:{id} VALUE 数据格式：String 模版信息
     */
    String SMS_TEMPLATE = "sms_template";


    /**
     * api授权登录随机字符串nonce的缓存
     * <p>
     * KEY 格式：login_fail_count:{username} VALUE 数据格式：String 登录失败次数
     */
    String LOGIN_API_NONCE = "login_api_nonce:";


    /**
     * 门店服务包开通关系缓存
     * <p>
     * KEY 格式：system:service_pack_relation:{method}:{tenantId} VALUE 数据格式：String
     */
    String SYSTEM_SERVICE_PACK_RELATION_KEY = "system:service_pack_relation:";

    /**
     * 小程序订阅模版的缓存
     * <p>
     * KEY 格式：wxa_subscribe_template:{userType} VALUE 数据格式 String, 模版信息
     */
    String WXA_SUBSCRIBE_TEMPLATE = "wxa_subscribe_template";

    /**
     * 账号登录失败次数的缓存
     * <p>
     * KEY 格式：login_fail_count:{username} VALUE 数据格式：String 登录失败次数
     */
    String LOGIN_FAIL_COUNT = "login_fail_count:";

    /**
     * 账号登录失败次数的缓存
     * <p>
     * KEY 格式：update_finger_print_fail_count:{mobile} VALUE 数据格式：String 修改指纹密码校验次数
     */
    String UPDATE_FINGER_PRINT_FAIL_COUNT = "update_finger_print_fail_count:";


    /**
     * 修改手机号的失败次数
     * <p>
     * KEY 格式：change_mobile_fail_count:{mobile} VALUE 数据格式：String 失败次数
     */
    String CHANGE_MOBILE_FAIL_COUNT = "change_mobile_fail_count:";

    /**
     * 转让手机号的失败次数
     * <p>
     * KEY 格式：transfers_mobile_fail_count:{mobile} VALUE 数据格式：String 失败次数
     */
    String TRANSFERS_MOBILE_FAIL_COUNT = "transfers_mobile_fail_count:";

    /**
     * 用户保存人脸限制次数 / 天
     * <p>
     * KEY 格式：user.save.face.limit.count:{userId} VALUE 数据格式：String 操作次数
     */
    String USER_SAVE_FACE_LIMIT_COUNT = "user.save.face.limit.count:";

    /**
     * 绑定老荷叶手机号错误限制
     * <p>
     * KEY 格式：bind_old_hy_mobile_fail_count:{mobile} VALUE 数据格式：String 失败次数
     */
    String BIND_OLD_HY_MOBILE_FAIL_COUNT = "bind_old_hy_mobile_fail_count:";
}
