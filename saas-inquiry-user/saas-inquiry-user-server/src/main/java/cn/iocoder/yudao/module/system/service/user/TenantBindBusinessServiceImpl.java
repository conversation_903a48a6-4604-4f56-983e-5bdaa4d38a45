package cn.iocoder.yudao.module.system.service.user;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.CHANGE_MOBILE_FAIL_LOCKED;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_BIND_OLD_HY_MOBILE_HAS_BIND_FAIL;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_BIND_OLD_HY_UN_EXISTS_FAIL;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_BIND_OLD_HY_UN_STORE_FAIL;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_NOT_EXISTS;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.api.sms.SmsCodeApi;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthSmsSendReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantBindReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantBindRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantBindStoreRespVO;
import cn.iocoder.yudao.module.system.convert.auth.AuthConvert;
import cn.iocoder.yudao.module.system.convert.tenant.TenantBindConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.ykq.SaasDrugstoreDO;
import cn.iocoder.yudao.module.system.dal.dataobject.ykq.YkqAccountInfoDO;
import cn.iocoder.yudao.module.system.dal.dataobject.ykq.YkqAccountRelationPoiDO;
import cn.iocoder.yudao.module.system.dal.dataobject.ykq.YkqDrugstoreDO;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantMapper;
import cn.iocoder.yudao.module.system.dal.mysql.ykq.SaasDrugstoreMapper;
import cn.iocoder.yudao.module.system.dal.mysql.ykq.YkqAccountInfoMapper;
import cn.iocoder.yudao.module.system.dal.mysql.ykq.YkqAccountRelationPoiMapper;
import cn.iocoder.yudao.module.system.dal.mysql.ykq.YkqDrugstoreMapper;
import cn.iocoder.yudao.module.system.dal.redis.RedisKeyConstants;
import cn.iocoder.yudao.module.system.dal.redis.common.UserLockRedisDAO;
import cn.iocoder.yudao.module.system.enums.sms.SmsSceneEnum;
import cn.iocoder.yudao.module.system.service.tenant.TenantService;
import com.alibaba.excel.util.StringUtils;
import jakarta.annotation.Resource;

import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 后台用户 Service 接口
 *
 * <AUTHOR>
 */
@Service
public class TenantBindBusinessServiceImpl implements TenantBindBusinessService {

    @Resource
    private SmsCodeApi smsCodeApi;

    @Resource
    private TenantMapper tenantMapper;

    @Resource
    private TenantService tenantService;

    @Resource
    private UserLockRedisDAO userLockRedisDAO;

    @Resource
    private SaasDrugstoreMapper saasDrugstoreMapper;

    @Resource
    private YkqAccountInfoMapper ykqAccountInfoMapper;

    @Resource
    private YkqAccountRelationPoiMapper ykqAccountRelationPoiMapper;

    @Resource
    private YkqDrugstoreMapper ykqDrugstoreMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long bindOldHySystem(TenantBindReqVO bindReqVO) {

        Long tenantId = Optional.ofNullable(bindReqVO.getTenantId()).orElse(TenantContextHolder.getRequiredTenantId());

        TenantDO tenantDO = tenantService.validTenant(tenantId);
        // 非换绑，又绑定过，则不允许再次绑定
        if (!bindReqVO.isReBind() && StringUtils.isNotBlank(tenantDO.extGet().getOrganSign())) {
            throw new RuntimeException("门店已绑定，如需绑定请解绑后再重新绑定");
        }

        // 判断当前门店是否被绑定
        if (tenantMapper.selectHyOrganSign(bindReqVO.getHyOrganSign()) != null) {
            throw new RuntimeException("门店已绑定");
        }
        // 更新门店绑定信息
        tenantMapper.updateById(new TenantDO().setId(tenantDO.getId()).setExt(tenantDO.extGet().setOrganSign(bindReqVO.getHyOrganSign())));

        return tenantDO.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long bindOldHyUnBind(Long tenantId) {

        tenantId = Optional.ofNullable(tenantId).orElse(TenantContextHolder.getRequiredTenantId());

        TenantDO tenantDO = tenantService.validTenant(tenantId);

        // 更新门店绑定信息
        tenantMapper.updateById(new TenantDO().setId(tenantDO.getId()).setExt(tenantDO.extGet().setOrganSign("").setHyId("")));

        return tenantDO.getId();
    }

    @Override
    public void senBindSmsCode(AuthSmsSendReqVO reqVO) {
        // 校验手机号再老荷叶中是否存在
        List<TenantBindStoreRespVO> hyDrugStores = validAndGetHyDrugStores(reqVO.getMobile());
        // 存在多个就返回
        long count = hyDrugStores.stream().filter(e -> !e.isExistBind()).count();

        if (count <= 0) {
            throw exception(TENANT_BIND_OLD_HY_MOBILE_HAS_BIND_FAIL);
        }
        // 发送验证码
        smsCodeApi.sendSmsCode(AuthConvert.INSTANCE.convert(reqVO).setCreateIp(getClientIP()));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public TenantBindRespVO bindOldHyUser(TenantBindReqVO reqVO) {

        // 1.如果根据手机号查到多个门店，返回门店列表让用户选择后再调用此接口
        // 非确认才校验验证码
        if (!reqVO.isConfirm()) {
            // 校验手机号
            userLockRedisDAO.isLocked(RedisKeyConstants.BIND_OLD_HY_MOBILE_FAIL_COUNT, reqVO.getMobile(), CHANGE_MOBILE_FAIL_LOCKED);
            // 校验验证码
            try {
                smsCodeApi.useSmsCode(AuthConvert.INSTANCE.convertBindOldHy(reqVO, SmsSceneEnum.TENANT_BIND_OLD_HY.getScene(), getClientIP()));
            } catch (ServiceException e) {
                userLockRedisDAO.lockRecord(RedisKeyConstants.BIND_OLD_HY_MOBILE_FAIL_COUNT, reqVO.getMobile());
                throw e;
            }
            List<TenantBindStoreRespVO> hyDrugStores = validAndGetHyDrugStores(reqVO.getMobile());

            long count = hyDrugStores.stream().filter(e -> !e.isExistBind()).count();
            if (count <= 0) {
                throw exception(TENANT_BIND_OLD_HY_MOBILE_HAS_BIND_FAIL);
            }
            // 存在多个就返回
            if (count >= 2) {
                return new TenantBindRespVO().setBindList(hyDrugStores).setBind(false);
            }
            // 2.如果根据手机号仅查到一个门店，直接绑定，无需校验
            reqVO.setConfirm(true);
            reqVO.setHyOrganSign(hyDrugStores.stream().filter(e -> !e.isExistBind()).findFirst().get().getOrganSign());
        }

        // 获取门店信息
        SaasDrugstoreDO saasDrugstoreDO = saasDrugstoreMapper.selectOne(SaasDrugstoreDO::getOrganSign, reqVO.getHyOrganSign());
        if (saasDrugstoreDO == null) {
            throw exception(TENANT_NOT_EXISTS);
        }
        if (tenantMapper.selectHyOrganSign(reqVO.getHyOrganSign()) != null) {
            throw new RuntimeException("门店已绑定");
        }
        Long tenantId = Optional.ofNullable(reqVO.getTenantId()).orElse(TenantContextHolder.getRequiredTenantId());
        TenantDO tenantDO = tenantService.validTenant(tenantId);
        // 更新门店绑定信息
        tenantMapper.updateById(new TenantDO().setId(tenantDO.getId()).setExt(tenantDO.extGet().setOrganSign(reqVO.getHyOrganSign())));

        return new TenantBindRespVO().setBind(true);
    }


    private List<TenantBindStoreRespVO> validAndGetHyDrugStores(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            throw new RuntimeException("手机号不能为空");
        }
        List<YkqAccountInfoDO> ykqAccountInfoDOS = ykqAccountInfoMapper.selectByPhone(mobile);
        if (ykqAccountInfoDOS.isEmpty()) {
            throw exception(TENANT_BIND_OLD_HY_UN_EXISTS_FAIL);
        }
        List<YkqAccountRelationPoiDO> ykqAccountRelationPoiDOS = ykqAccountRelationPoiMapper.selectByAccountIds(ykqAccountInfoDOS.stream().map(YkqAccountInfoDO::getAccountId).toList());
        if (ykqAccountRelationPoiDOS.isEmpty()) {
            throw exception(TENANT_BIND_OLD_HY_UN_EXISTS_FAIL);
        }
        List<YkqDrugstoreDO> ykqDrugstoreDOS = ykqDrugstoreMapper.selectAvailableByIds(ykqAccountRelationPoiDOS.stream().map(d -> NumberUtils.toInt(d.getPoiId(), -1)).toList());
        if (ykqDrugstoreDOS.isEmpty()) {
            throw exception(TENANT_BIND_OLD_HY_UN_STORE_FAIL);
        }
        List<SaasDrugstoreDO> saasDrugstoreDOS = saasDrugstoreMapper.selectListByOrganSigns(ykqDrugstoreDOS.stream().map(YkqDrugstoreDO::getLzId).toList());
        if (saasDrugstoreDOS.isEmpty()) {
            throw exception(TENANT_BIND_OLD_HY_UN_STORE_FAIL);
        }

        // 判断是否存在新荷叶绑定
        List<String> list = tenantMapper.selectListHyOrganSigns(saasDrugstoreDOS.stream().map(SaasDrugstoreDO::getOrganSign).toList()).stream().map(s -> s.extGet().getOrganSign()).toList();

        return saasDrugstoreDOS.stream().map(s -> {
            TenantBindStoreRespVO b = TenantBindConvert.INSTANCE.convert(s);
            b.setExistBind(list.contains(s.getOrganSign()));
            return b;
        }).toList();
    }
}
