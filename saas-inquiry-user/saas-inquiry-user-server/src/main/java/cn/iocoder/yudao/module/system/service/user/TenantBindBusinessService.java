package cn.iocoder.yudao.module.system.service.user;

import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthSmsSendReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantBindReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantBindRespVO;
import jakarta.validation.Valid;

/**
 * 后台用户 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantBindBusinessService {

    /**
     * 绑定老荷叶门店
     *
     * @param tenantId
     * @param hyId
     * @return
     */
    Long bindOldHySystem(TenantBindReqVO bindReqVO);

    /**
     * 解绑老荷叶门店
     *
     * @param tenantId
     * @return
     */
    Long bindOldHyUnBind(Long tenantId);

    /**
     * 发送绑定验证码
     *
     * @param sendReqVO
     */
    void senBindSmsCode(AuthSmsSendReqVO sendReqVO);

    /**
     * 绑定老荷叶门店用户
     *
     * @param reqVO
     * @return
     */
    TenantBindRespVO bindOldHyUser(@Valid TenantBindReqVO reqVO);
}
