package cn.iocoder.yudao.module.system.dal.mysql.ykq;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.system.dal.dataobject.ykq.YkqAccountInfoDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 账号主 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface YkqAccountInfoMapper extends BaseMapperX<YkqAccountInfoDO> {

    default List<YkqAccountInfoDO> selectByPhone(String phone) {
        return selectList(YkqAccountInfoDO::getBindPhone, phone, YkqAccountInfoDO::getIsDeleted, 0);
    }
}