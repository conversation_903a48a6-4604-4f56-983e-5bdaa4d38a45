package cn.iocoder.yudao.module.system.dal.mysql.ykq;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.dal.dataobject.ykq.YkqDrugstoreDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 药店管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface YkqDrugstoreMapper extends BaseMapperX<YkqDrugstoreDO> {

    default List<YkqDrugstoreDO> selectAvailableByIds(List<Integer> ids) {
        return selectList(new LambdaQueryWrapperX<YkqDrugstoreDO>()
            .in(YkqDrugstoreDO::getId, ids)
            .eq(YkqDrugstoreDO::getStatus, 1));
    }

}