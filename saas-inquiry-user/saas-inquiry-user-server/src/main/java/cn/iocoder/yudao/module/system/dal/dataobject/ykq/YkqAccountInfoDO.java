package cn.iocoder.yudao.module.system.dal.dataobject.ykq;

import lombok.*;
import java.io.Serializable;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 账号主 DO
 *
 * <AUTHOR>
 */
@TableName("ykq_account_info")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YkqAccountInfoDO implements Serializable {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 账号ID
     */
    private String accountId;
    /**
     * 父账号account_id默认0
     */
    private String parentId;
    /**
     * 用户昵称
     */
    private String nickName;
    /**
     * 接入方appkey如ykq_account_platform
     */
    private String appKey;
    /**
     * 手机号必传（否则后续绑定解绑时易出现账号合并问题）子账号时可以没有，没有时不能用手机验证码登录；同一平台下手机号不许重复
     */
    private String bindPhone;
    /**
     * 账号状态:0 禁用 1 有效 2 冻结 3 注销
     */
    private Integer status;
    /**
     * 是否测试账号:0 测试账号 1 正常账号
     */
    private Integer isTest;
    /**
     * 连锁账号：0：非连锁；1：连锁
     */
    private Integer isChain;
    /**
     * ykq_account_platform表source
     */
    private Integer source;
    /**
     * 是否删除，0未删除，1已经删除
     */
    private Integer isDeleted;
    /**
     * 未知:0，运营后台:1,商家端:2,商家端店员创建:3,BD海报(H5):4,智慧脸:5,EC:6,药智慧:7,0元购:8,备用1:9，备用2:10
     */
    private Integer registerType;

}