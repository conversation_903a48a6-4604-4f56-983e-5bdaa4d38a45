package cn.iocoder.yudao.module.system.dal.dataobject.ykq;

import lombok.*;
import java.io.Serializable;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 药店机构 DO
 *
 * <AUTHOR>
 */
@TableName("saas_drugstore")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaasDrugstoreDO implements Serializable {

    /**
     * 主键
     */
    @TableId
    private Integer id;
    /**
     * 药店机构标识
     */
    private String organSign;
    /**
     * 药店负责人用户id
     */
    private Long managerUserId;
    /**
     * 药店简介名称
     */
    private String drugstoreName;
    /**
     * 药店负责人姓名
     */
    private String managerName;
    /**
     * 药店联系电话
     */
    private String contactPhone;
    /**
     * 省
     */
    private String province;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市
     */
    private String city;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 区
     */
    private String area;
    /**
     * 区编码
     */
    private String areaCode;
    /**
     * 药店地址
     */
    private String address;
    /**
     * 签约类型（年度付费，按单付费）
     */
    private String typeContract;
    /**
     * 签约时间
     */
    private LocalDateTime contractTime;
    /**
     * 药店状态 1已开通 2未开通
     */
    private Integer status;
    /**
     * 营业执照名称
     */
    private String businessLicenseName;
    /**
     * 营业执照号
     */
    private String businessLicenseNumber;
    /**
     * 营业执照
     */
    private String businessLicenseImg;
    /**
     * 药品经营许可证
     */
    private String pharmaceuticalTradingLicenseImg;
    /**
     * 药品经营质量管理规范认证号
     */
    private String qualityManagementLicenseNumber;
    /**
     * 药品经营质量管理规范认证证书
     */
    private String qualityManagementLicenseImg;
    /**
     * 药店推荐码
     */
    private String referralCode;
    /**
     * 远程处方服务: 0处方+审方(平台) 1处方
     */
    private Integer serverType;
    /**
     * 环境标志：0-真实数据；1-测试数据；2-线上演示数据
     */
    private Integer envTag;
    /**
     * saas是否开通：0黑名单，1白名单
     */
    private Integer saasWhiteStatus;
    /**
     * 门店来源，0智慧脸，1宜块钱，2灵芝
     */
    private Integer source;
    /**
     * 药店类型，0单体 1连锁
     */
    private Integer drugstoreType;
    /**
     * 是否付费用户，0否1是
     */
    private Integer payingUsers;
    /**
     * 用户中心user_id
     */
    private Long userId;
    /**
     * 是否同步药监局 0 不同步 1 同步
     */
    private Integer sycSpda;
    /**
     * 豆芽门店id
     */
    private Integer poiId;
    /**
     * 门店与豆芽签约状态，0:未签约;1:已签约
     */
    private Boolean signStatus;
    /**
     * 许可证编号
     */
    private String storeNo;
    /**
     * 远程审方服务 0 未开通 1已开通
     */
    private Integer remoteAudit;
    /**
     * 智慧脸机构号
     */
    private String smartfaceOrganSign;
    /**
     * 门店二维码
     */
    private String drugstoreQrCode;
    /**
     * 灵芝管理合同
     */
    private String lzManagementLicense;
    /**
     * 备案合同及保密协议
     */
    private String recordContractsAndConfidentialityAgreements;
    /**
     * 远程审方备案件
     */
    private String remoteAuditArchiveDocuments;
    /**
     * 第三方审方渠道id
     */
    private Integer thirdAuditChannel;
    /**
     * 启用禁用：0 启用 1 禁用
     */
    private Integer enable;
    /**
     * 荷叶id
     */
    private String hyId;
    /**
     * 荷叶公众号开通状态：0 开通 1 未开通
     */
    private Integer hyOfficialAccountsStatus;
    /**
     * 微信公众号二维码
     */
    private String hyOfficialAccountsQrCode;
    /**
     * 药品经营许可证注册日期
     */
    private LocalDateTime pharmaceuticalTradingLicenseRegistryTime;
    /**
     * 药品经营许可证有效期
     */
    private LocalDateTime pharmaceuticalTradingLicenseValidityPeriod;
    /**
     * 0 中台 1 品牌
     */
    private Integer drugChannel;
    /**
     * 所属互联网医院 1:海南宜贰叁互联网医院 2:成都双流互联网医院 3:武汉宜贰叁互联网医院
     */
    private Integer hospitalCode;
    /**
     * 日期显示类型 0未设置 1年月日-时分秒；2年月日
     */
    private Integer dateType;
    /**
     * 精度
     */
    private Double longitude;
    /**
     * 纬度
     */
    private Double latitude;
    /**
     * 百度地图定位地址
     */
    private String mapLocationAddress;
    /**
     * 限制距离开关 0 关闭 1 开启
     */
    private Integer distanceLimitSwitch;
    /**
     * 三方门店id
     */
    private String thirdStoreId;
    /**
     * 总部名称
     */
    private String headquartersDrugstoreName;
    /**
     * 迁移状态 0未迁移 1待迁移 2门店已迁移 3迁移成功
     */
    private Integer migrationStatus;
    /**
     * 迁移时间
     */
    private LocalDateTime migrationTime;

}