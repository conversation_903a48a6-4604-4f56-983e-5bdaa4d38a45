package cn.iocoder.yudao.module.system.convert.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.ip.core.Area;
import cn.iocoder.yudao.framework.ip.core.enums.AreaTypeEnum;
import cn.iocoder.yudao.framework.ip.core.utils.AreaUtils;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantRespDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.biz.TenantBizRelationSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.certificate.TenantCertificateRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.certificate.TenantCertificateSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.excel.TenantAndPackageExcelVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantBindStoreRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantInfoVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSimpleRespVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.ykq.SaasDrugstoreDO;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionAuditTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantCertificateTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import com.xyy.saas.inquiry.mq.tenant.TenantInfoUpdateMessageDto;
import com.xyy.saas.inquiry.mq.tenant.TenantParamConfigDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationDrugStoreRespDto;
import com.xyy.saas.inquiry.pojo.tenant.TenantExtDto;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 门店 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantBindConvert {

    TenantBindConvert INSTANCE = Mappers.getMapper(TenantBindConvert.class);

    TenantBindStoreRespVO convert(SaasDrugstoreDO s);
}
