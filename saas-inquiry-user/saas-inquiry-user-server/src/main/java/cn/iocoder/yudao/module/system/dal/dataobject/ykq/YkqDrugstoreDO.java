package cn.iocoder.yudao.module.system.dal.dataobject.ykq;

import lombok.*;
import java.io.Serializable;
import java.util.*;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 药店管理 DO
 *
 * <AUTHOR>
 */
@TableName("ykq_drugstore")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YkqDrugstoreDO implements Serializable {

    /**
     * ID
     */
    @TableId
    private Integer id;
    /**
     * 荷叶ID
     */
    private String hyId;
    /**
     * 灵芝ID
     */
    private String lzId;
    /**
     * 开通宜块钱服务 1是0否
     */
    private Integer establishYkqService;
    /**
     * 开通灵芝服务 1是0否
     */
    private Integer establishLzService;
    /**
     * 药店机构码
     */
    private String organSign;
    /**
     * 药帮忙账号
     */
    private String drugstoreAccount;
    /**
     * 药店名称
     */
    private String drugstoreName;
    /**
     * 药店联系电话
     */
    private String tel;
    /**
     * 药店地址
     */
    private String address;
    /**
     * 经度
     */
    private Double longitude;
    /**
     * 维度
     */
    private Double latitude;
    /**
     * 是否支持医保(是:1 否:0 )
     */
    private Integer isMedicare;
    /**
     * 是否云医笔记合作店(是:1 否:0 )
     */
    private Integer isNoteCooperate;
    /**
     * 状态 1 启用 0 禁用
     */
    private Integer status;
    /**
     * 次
     */
    private LocalDateTime times;
    /**
     * 所属城市 先默认武汉
     */
    private String city;
    /**
     * 药店营业时间
     */
    private String businessHours;
    /**
     * 药店门头照
     */
    private String doorheadPhoto;
    /**
     * 省编号
     */
    private Integer provinceCode;
    /**
     * 市编号
     */
    private Integer cityCode;
    /**
     * 区编号
     */
    private Integer areaCode;
    /**
     * 是否测试药店，0:否，1:是
     */
    private Integer isTest;
    /**
     * 智慧脸药店名
     */
    private String smartfaceDrugstorename;
    /**
     * 是否支持同城闪送1支持 0不支持
     */
    private Integer isDelivery;
    /**
     * 同城闪送类型,1:店铺自配送,2:达达配送,当is_delivery为1是该字段才有作用
     */
    private Integer cityDeliveryType;
    /**
     * 智慧脸机构标识
     */
    private String smartfaceOrganSign;
    /**
     * 智慧脸机构名称
     */
    private String smartfaceOrganName;
    /**
     * 是否支持快递
     */
    private Integer expressed;
    /**
     * 药店分公司代码（机构标识）
     */
    private String institutionalDentification;
    /**
     * 营业起始时间 秒
     */
    private Integer openTime;
    /**
     * 营业结束时间 秒
     */
    private Integer closeTime;
    /**
     * 店铺二维码
     */
    private String qrcodeUrl;
    /**
     * 店铺公告
     */
    private String noticeContent;
    /**
     * 状态 1 单体店 2 连锁店
     */
    private Integer storeChainType;
    /**
     * 机构药帮忙机构代码 1 智慧脸机构代码 2
     */
    private Integer organSelectType;
    /**
     * 自动接单 0：否 1：是
     */
    private Integer autoAccept;
    /**
     * 是否支持在线订单配送(是:1 否:0 )
     */
    private Integer deliveryIsSupported;
    /**
     * 客服电话
     */
    private String servicePhone;
    /**
     * 药店类型
     */
    private String drugstoreType;
    /**
     * 药店类型名字
     */
    private String drugstoreTypeName;
    /**
     * 药店是否同步到ec宜块钱店铺白名单,1:有,0:没有
     */
    private Integer syncEc;
    /**
     * 是否支持邮寄配送0不支持1支持
     */
    private Integer hasExpress;
    /**
     * 白名单状态
     */
    private Integer writeStatus;
    /**
     * 是否支持到店自提 0 不支持 1支持
     */
    private Integer isPinckupin;
    /**
     * 同城闪送费用承担方 0 店铺 100宜块钱平台
     */
    private Integer deliveryAssumeStatus;
    /**
     * 快递邮寄费用承担方 0 店铺 100宜块钱平台
     */
    private Integer expressAssumeStatus;
    /**
     * 到店自提支付方式 0 到店支付 1在线支付
     */
    private Integer pinckupinPayStatus;
    /**
     * 同城闪送是否支持在线支付 0 不支持 1支持
     */
    private Integer deliveryPayStatus;
    /**
     * 邮寄是否支持在线支付 0 不支持 1支持
     */
    private Integer expressPayStatus;
    /**
     * 同城闪送费用
     */
    private BigDecimal deliveryCost;
    /**
     * 默认快递费用
     */
    private BigDecimal expressCost;
    /**
     * 是否重症药店 0 否 1是
     */
    private Integer isSeverecase;
    /**
     * 同步智慧脸价格和库存,0:不同步,1同步
     */
    private Integer syncSmartFace;
    /**
     * PDC药店主键
     */
    private Integer poiId;
    /**
     * 创建时间
     */
    private Integer ctime;
    /**
     * 修改时间
     */
    private Integer utime;
    /**
     * 0不设置,1满减
     */
    private Integer hasConditionExpress;
    /**
     * 满减邮寄费用
     */
    private BigDecimal conditionExpress;
    /**
     * 门店logo照
     */
    private String logoPhoto;
    /**
     * 店铺照
     */
    private String shopPhoto;
    /**
     * 货架照
     */
    private String shelvesPhoto;
    /**
     * 联系人
     */
    private String contacts;
    /**
     * 品牌ID
     */
    private String brandId;
    /**
     * 是否同步更新品牌信息 0:关闭、1:开启
     */
    private Integer isSynchronizedBrandInfo;
    /**
     * 经营范围集合
     */
    private String businessScopeCodeList;
    /**
     * 经营范围名称集合
     */
    private String businessScopeNameList;
    /**
     * 药店在宜块钱下单所用的id
     */
    private String puid;
    /**
     * 是否b2c药店
     */
    private Integer isB2c;
    /**
     * 是否支持医保在线支付,0:不支持,1:支持
     */
    private Integer securitycardOnlinePay;
    /**
     * 医疗机构编码
     */
    private String medicalOrganCode;
    /**
     * 推广人id
     */
    private Integer pusherUserId;
    /**
     * 订单是否同步智慧脸0不同步 1同步
     */
    private Integer orderSyncZhl;
    /**
     * 同城闪送服务费
     */
    private BigDecimal deliveryServiceCost;
    /**
     * 快递邮寄服务费
     */
    private BigDecimal expressServiceCost;
    /**
     * 店铺佣金
     */
    private BigDecimal commissionRatio;
    /**
     * 同城闪送满包邮金额
     */
    private BigDecimal conditionDelivery;
    /**
     * 0不设置,1满减
     */
    private Integer hasConditionDelivery;
    /**
     * 医保加密key
     */
    private String medicalEncryptionKey;
    /**
     * 是否是公域(1-是,0-否)
     */
    private Integer publicDomain;
    /**
     * 控制门店端是否可配置名医问诊和极速问诊 0 ：开启 1：关闭
     */
    private Integer hyInquiryConfigStatus;
    /**
     * 名医问诊配置 0 ：开启 1：关闭
     */
    private Integer famousDoctorConfigStatus;
    /**
     * 极速问诊配置 0 ：开启 1：关闭
     */
    private Integer speedConfigStatus;
    /**
     * 店铺来源
     */
    private Integer drugstoreSource;
    /**
     * 开通惠民保0不开通1开通
     */
    private Integer openHmyStatus;
    /**
     * 是否是医带患合作店铺 0-否1-是 默认否
     */
    private Integer doctorPatientCooperation;
    /**
     * 呼叫骑手方式 0:手动、1:自动
     */
    private Integer isAutoCall;
    /**
     * 开启语音播报 0:关闭、1:开启
     */
    private Integer voiceBroadcast;
    /**
     * 开启自动打印 0:关闭、1:开启
     */
    private Integer autoPrint;
    /**
     * 1-智鹿O2O 2-商城O2O
     */
    private Integer o2oFlag;
    /**
     * O2O佣金
     */
    private BigDecimal o2oCommission;
    /**
     * 商保O2O标记 1-商保O2O
     */
    private Integer insureO2oFlag;
    /**
     * POP标记 1-POP
     */
    private Integer popO2oFlag;
    /**
     * 是否使用电子面单 1-使用 0-不使用
     */
    private Integer waybillFlag;
    /**
     * 经营类型 0-处方药 1-甲类非处方药 2-乙类非处方药
     */
    private String manageType;
    /**
     * 蜂鸟账号类型 0-普通 1-品质达
     */
    private Integer eleType;
    /**
     * 是否是基金o2o 0-否 1-是
     */
    private Integer fundO2oFlag;
    /**
     * 是否配置保险o2o 0否，1是
     */
    private Integer hmInsureO2oFlag;
    /**
     * 是否C端展示 1 展示 0 不展示
     */
    private Integer showOnClient;
    /**
     * 是否合作网药 1 是 0 否
     */
    private Integer partnerOnlineStore;
    /**
     * 员福基金到店 1 勾选 0 未勾选
     */
    private Integer yfFundOfflineFlag;
    /**
     * 基金B2c 1-勾选 0-未勾选
     */
    private Integer fundB2cFlag;
    /**
     * 商城B2C 1-勾选 0-未勾选
     */
    private Integer mallB2cFlag;
    /**
     * 结算主体 0-药店 1-签约主体
     */
    private Integer settleSubject;
    /**
     * 签约主体 关联药店id
     */
    private Integer contractSubject;
    /**
     * O2O药店类型 见O2ODrugstoreType
     */
    private Integer o2oDrugstoreType;
    /**
     * 药店简介
     */
    private String introduction;
    /**
     * ERP药店id
     */
    private String erpDrugstoreId;
    /**
     * 配送类型 0-平台配送 1-商家自配
     */
    private Integer deliveryType;
    /**
     * 是否是供应商：0-否，1-是
     */
    private Integer popSupplyFlag;
    /**
     * 商家结算金额是否扣除平台佣金 0-否 1-是 默认=否
     */
    private Integer settleDeductCommissionFlag;
    /**
     * 包邮计算规则：1-实付价，2-原价
     */
    private Integer expressPostageRule;

}