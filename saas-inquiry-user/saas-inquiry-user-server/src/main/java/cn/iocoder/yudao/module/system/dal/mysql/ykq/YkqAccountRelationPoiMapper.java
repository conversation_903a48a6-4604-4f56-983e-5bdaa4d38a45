package cn.iocoder.yudao.module.system.dal.mysql.ykq;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.dal.dataobject.ykq.YkqAccountRelationPoiDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 账号门店对应关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface YkqAccountRelationPoiMapper extends BaseMapperX<YkqAccountRelationPoiDO> {

    default List<YkqAccountRelationPoiDO> selectByAccountIds(List<String> accountIds) {
        return selectList(new LambdaQueryWrapperX<YkqAccountRelationPoiDO>()
            .in(YkqAccountRelationPoiDO::getAccountId, accountIds)
            .eq(YkqAccountRelationPoiDO::getStatus, 0)
        );
    }
}