package cn.iocoder.yudao.module.system.convert.auth;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.filterList;
import static cn.iocoder.yudao.module.system.dal.dataobject.permission.MenuDO.ID_ROOT;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.api.sms.dto.code.SmsCodeSendReqDTO;
import cn.iocoder.yudao.module.system.api.sms.dto.code.SmsCodeUseReqDTO;
import cn.iocoder.yudao.module.system.api.social.dto.SocialUserBindReqDTO;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthLoginRespVO;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthPermissionInfoRespVo;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthSmsLoginReqVO;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthSmsSendReqVO;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthSocialLoginReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantBindReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantTransfersVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.profile.UserProfileChangeMobileReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.MenuDO;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.enums.permission.MenuTypeEnum;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import jakarta.validation.Valid;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.slf4j.LoggerFactory;

@Mapper
public interface AuthConvert {

    AuthConvert INSTANCE = Mappers.getMapper(AuthConvert.class);

    AuthLoginRespVO convert(OAuth2AccessTokenDO bean);

    default AuthPermissionInfoRespVo convert(AdminUserRespDTO user, TenantRespVO tenantRespVO, List<RoleDO> roleList, List<MenuDO> menuList) {
        Set<String> roleCodes = CollectionUtils.convertSet(roleList, RoleDO::getCode);
        // app tabBar页面
        List<MenuDO> tabBarMenus = new ArrayList<>();
        MenuDO parentMenuDO = menuList.stream().filter(menu -> Objects.equals(menu.getParentId(), ID_ROOT)).findFirst().orElse(null);
        if (parentMenuDO != null) {
            tabBarMenus = menuList.stream().filter(m -> Objects.equals(m.getParentId(), parentMenuDO.getId())).toList(); // tabBar 菜单列表
        }
        return AuthPermissionInfoRespVo.builder()
            .user(BeanUtils.toBean(user, AuthPermissionInfoRespVo.UserVO.class))
            .tenant(BeanUtils.toBean(tenantRespVO, AuthPermissionInfoRespVo.TenantVO.class))
            .admin(Objects.equals(user.getId(), tenantRespVO.getContactUserId()))
            .drugStore(!TenantConstant.isSystemTenant())
            .pharmacist(roleCodes.contains(RoleCodeEnum.PHARMACIST.getCode()))
            .physician(roleCodes.contains(RoleCodeEnum.DOCTOR.getCode()))
            .roles(convertSet(roleList, RoleDO::getCode))
            .tabBarMenus(AuthConvert.INSTANCE.convertMenuVos(tabBarMenus))
            // 权限标识信息
            .permissions(convertSet(menuList, MenuDO::getPermission))
            // 菜单树
            .menus(buildMenuTree(menuList))
            .build();
    }

    AuthPermissionInfoRespVo.MenuVO convertTreeNode(MenuDO menu);

    /**
     * 将菜单列表，构建成菜单树
     *
     * @param menuList 菜单列表
     * @return 菜单树
     */
    default List<AuthPermissionInfoRespVo.MenuVO> buildMenuTree(List<MenuDO> menuList) {
        if (CollUtil.isEmpty(menuList)) {
            return Collections.emptyList();
        }
        // 移除按钮
        menuList.removeIf(menu -> menu.getType().equals(MenuTypeEnum.BUTTON.getType()));
        // 排序，保证菜单的有序性
        menuList.sort(Comparator.comparing(MenuDO::getSort));

        // 构建菜单树
        // 使用 LinkedHashMap 的原因，是为了排序 。实际也可以用 Stream API ，就是太丑了。
        Map<Long, AuthPermissionInfoRespVo.MenuVO> treeNodeMap = new LinkedHashMap<>();
        menuList.forEach(menu -> treeNodeMap.put(menu.getId(), AuthConvert.INSTANCE.convertTreeNode(menu)));
        // 处理父子关系
        treeNodeMap.values().stream().filter(node -> !node.getParentId().equals(ID_ROOT)).forEach(childNode -> {
            // 获得父节点
            AuthPermissionInfoRespVo.MenuVO parentNode = treeNodeMap.get(childNode.getParentId());
            if (parentNode == null) {
                LoggerFactory.getLogger(getClass()).error("[buildRouterTree][resource({}) 找不到父资源({})]",
                    childNode.getId(), childNode.getParentId());
                return;
            }
            // 将自己添加到父节点中
            if (parentNode.getChildren() == null) {
                parentNode.setChildren(new ArrayList<>());
            }
            parentNode.getChildren().add(childNode);
        });
        // 获得到所有的根节点 // 如果是app 剔除掉第一节点
        List<AuthPermissionInfoRespVo.MenuVO> menuVOS = filterList(treeNodeMap.values(), node -> ID_ROOT.equals(node.getParentId()));
        if (CollUtil.isNotEmpty(menuVOS) && Objects.equals(menuVOS.getFirst().getType(), MenuTypeEnum.APP_MENU.getType())) {
            return menuVOS.getFirst().getChildren();
        }
        return menuVOS;
    }

    SocialUserBindReqDTO convert(Long userId, Integer userType, AuthSocialLoginReqVO reqVO);

    SmsCodeSendReqDTO convert(AuthSmsSendReqVO reqVO);

    SmsCodeUseReqDTO convert(AuthSmsLoginReqVO reqVO, Integer scene, String usedIp);

    List<AuthPermissionInfoRespVo.MenuVO> convertMenuVos(List<MenuDO> tabBarMenus);

    SmsCodeUseReqDTO convertChangeMobile(UserProfileChangeMobileReqVO reqVO, Integer scene, String usedIp);

    SmsCodeUseReqDTO convertTransfers(TenantTransfersVO transfersVO, Integer scene, String usedIp);

    SmsCodeUseReqDTO convertBindOldHy(TenantBindReqVO transfersVO, Integer scene, String usedIp);
}
