package com.xyy.saas.inquiry.hospital.api.doctor.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName：InquiryDoctorDto
 * @Author: xucao
 * @Date: 2024/10/25 10:55
 * @Description: 问诊医生信息
 */
@Data
public class InquiryDoctorDto implements Serializable {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 医生编码
     */
    private String pref;
    /**
     * 医生名称
     */
    private String name;
    /**
     * 性别 1男 2女
     */
    private Integer sex;
    /**
     * 身份证号码
     */
    private String idCard;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 审核状态 0、待审核  1、审核通过  2、审核驳回
     */
    private Integer auditStatus;
    /**
     * 合作状态：0未合作 1 合作中 2禁用合作 3过期
     */
    private Integer cooperation;

    /**
     * 在线状态：0离线  1在线
     */
    private Integer onlineStatus;
    /**
     * 开始接诊时间
     */
    private LocalDateTime startInquiryTime;
    /**
     * 结束停诊时间
     */
    private LocalDateTime endInquiryTime;
    /**
     * 环境标志：prod-真实数据；test-测试数据；show-线上演示数据
     */
    private String envTag;
    /**
     * 证件照地址
     */
    private String photo;
    /**
     * 个人简介
     */
    private String biography;
    /**
     * 擅长专业,eg:擅长神经内科诊疗
     */
    private String professionalDec;
    /**
     * 医生类型： 1全职医生 2兼职医生
     */
    private Integer jobType;
    /**
     * 是否开启密码,0:不开启,1:开启
     */
    private Boolean prescriptionPasswordStatus;
    /**
     * 开方密码
     */
    private String prescriptionPassword;
    /**
     * 是否禁用
     */
    private Boolean disable;

    /**
     * 环境
     */
    private String env;

    /**
     * 医生医保编码
     */
    private String doctorMedicareNo;

    /**
     * 专业职称代码，例如：2
     */
    private Integer titleCode;

    /**
     * 医生在医院下的编码
     */
    private String doctorHospitalPref;

    /**
     * 拓展字段
     */
    private InquiryDoctorExtDto ext;

    private String professionalNo;

}
