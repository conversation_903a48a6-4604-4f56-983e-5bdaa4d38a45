package com.xyy.saas.inquiry.hospital.api.doctor.dto;

import com.xyy.saas.inquiry.hospital.enums.DoctorFillingStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @ClassName：InquiryDoctorDto
 * @Author: xucao
 * @Date: 2024/10/25 10:55
 * @Description: 问诊医生信息
 */
@Data
@Accessors(chain = true)
public class InquiryDoctorExtDto implements Serializable {

    /**
     * 陕西监管备案状态 0:未备案 1:已备案
     * see {@link DoctorFillingStatusEnum}
     */
    @Schema(description = "陕西监管备案状态 0:未备案 1:已备案")
    private Integer fillingStatus4ShaanxiRegulatory = DoctorFillingStatusEnum.NOT.getType();

}
