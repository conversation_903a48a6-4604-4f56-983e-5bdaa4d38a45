package com.xyy.saas.inquiry.util;

import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static org.junit.jupiter.api.Assertions.*;


/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
class SpecParserTest {

    @Test
    void parseMinimumPackageQuantityTest() {
        System.out.println("--- 测试开始 ---");

        Map<String, Integer> testCases = new HashMap<>() {{
            put(null, null);
            put("", null);
            put("  ", null);
            put("118s", 118);
            put("60mL", 1);
            put("0.1% 5ml:5mg", 1);
            put("24s*2板", 48);
            put("47.5mg*7s", 7);
            put("3%(5ml:150mg)*1支", 1);
            put("5ml:15mg*6ml", 1);
            put("125ug*60揿", 1);
            put("10s*2板*3小盒", 60);
            put("47.5mg*14s*2板 薄膜衣", 28);
            put("0.25g*10s*2板*3小盒 糖衣", 60);
            put("5cm*7cm*6贴", 6);
            put("7cm*10cm*5贴*2袋", 10);
            put("5cm*6cm*2贴*4片*2袋", 16);
        }};

        testCases.forEach((spec, expected) -> {
            Integer result = SpecParser.parseMinimumPackageQuantity(spec);
            // 断言结果是否符合预期
            assertEquals(expected, result, "规格: " + spec);
        });

        System.out.println("--- 测试结束 ---");
    }
}