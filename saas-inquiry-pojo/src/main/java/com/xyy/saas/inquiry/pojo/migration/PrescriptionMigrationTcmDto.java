package com.xyy.saas.inquiry.pojo.migration;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2019/09/11
 * @Describe
 */
@Data
public class PrescriptionMigrationTcmDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 中药总副数
     */
    private BigDecimal tcmTotalDosage;

    /**
     * 中药每日副数
     */
    private BigDecimal tcmDailyDosage;

    /**
     * 中药每日频次
     */
    private String tcmDailyFrequency;

    /**
     * 中药用法
     */
    private String tcmUsage;

    /**
     * 中药加工方式
     */
    private String tcmProcessingMethod;


}