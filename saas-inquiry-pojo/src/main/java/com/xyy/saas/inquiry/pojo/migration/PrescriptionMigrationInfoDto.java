package com.xyy.saas.inquiry.pojo.migration;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2019/09/11
 * @Describe
 */
@Data
@Slf4j
public class PrescriptionMigrationInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 处方ID
     */
    private Long id;

    /**
     * 处方guid
     */
    private String guid;

    /**
     * 处方号
     */
    private String pref;

    /**
     * 提交门店
     */
    private String drugstoreName;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 患者年龄
     */
    private String patientAge;
    /**
     * 患者性别
     */
    private String patientSex;

    /**
     * 医师姓名
     */
    private String physicianName;

    /**
     * 开方时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outPrescriptionTime;

    /**
     * 审核药师
     */
    private String pharmacistName;

    /**
     * 审批状态
     */
    private Integer auditStatus;

    /**
     * 诊断说明(临床诊断)
     */
    private String diagnosis;

    private String diagnosisCode;

    /**
     * 处方明细（ES中存储为text类型的JSON字符串）
     */
    private String prescriptionDetails;

    /**
     * 审方类型 0 正常审方    1带方审方
     */
    private Integer auditType;

    /**
     * 开方医院
     */
    private String hosId;

    /**
     * 视频URL
     */
    private String mp4Url;

    /**
     * 问诊Guid
     */
    private String inquiryGuid;

    /**
     * 问诊状态
     */
    private String inquiryStatus;

    /**
     * 审核不通过的原因
     */
    private String invalidInfo;

    /**
     * 上传时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approvalTime;

    /**
     * 患者GUID
     */
    private String patientGuid;

    /**
     * 患者身份证号
     */
    private String idCard;

    /**
     * 患者手机号
     */
    private String telephone;

    /**
     * 过敏史 1否 2有
     */
    private Integer allergySymptom;

    /**
     * 审核结果 0否 1是
     */
    private Integer invalidStatus;

    private String organSign;

    /**
     * 处方类型：0视频处方，1图文处方 2 极速问诊
     */
    private Integer type;

    /**
     * 处方来源：0灵芝问诊，1宜块钱，2 SaaS
     */
    private Integer source;

    // 主诉
    private String mainSuit;

    // 过敏史说明
    private String allergySymptomExplain;
    // 既往病史
    private String medicalHistory;

    // 处方状态 1 待开方 2 已关闭 3 已超时  4 开方成功、等待上传 5 上传成功 6 待签名 7 医师签名成功 8 医师签名失败 9 药师签名成功 10 药师签名失败
    private Integer status;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * impdf
     */
    private String imPdf;

    /**
     * 问诊来源
     */
    private Integer inquirySource;


    private String diagnosisPdfUrl;

    /**
     * 药品类型 0 西药  1 中药
     */
    private Integer medicineType;

    /**
     * 接诊时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;


    private String inquiryNo;

    /**
     * 复诊图片url（ES中存储为text类型的JSON字符串）
     */
    private String checkImageList;

    /**
     * 金额
     */
    private String amount;


    private String hosPhaReview;
    /**
     * 医院药师名称
     */
    private String hosPharmacistName;

    /**
     * 医院药师审核时间
     */
    private String hosApprovalTime;

    /**
     * 处方图片
     */
    private String prescriptionImage;

    /**
     * 处方统筹类型
     */
    private String overallPlanType;

    private String physicianGuid;


    private String mp3Url;
    /**
     * 处方药品名称
     */
    private String productName;
    /**
     * 中药用法用量
     */
    private String dosageAndUsage;

    /**
     * 妊娠哺乳期
     */
    private String pregnancyAndLactationValue;
    /**
     * 肝肾功能异常
     */
    private String liverAndRenalFunction;
    /**
     * 医生id
     */
    private String physicianId;

    /**
     * 客户端类型
     */
    private Integer clientType;

}