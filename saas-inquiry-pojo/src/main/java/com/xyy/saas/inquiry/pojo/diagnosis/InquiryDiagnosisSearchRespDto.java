package com.xyy.saas.inquiry.pojo.diagnosis;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2024/11/25 20:41
 */
@Schema(description = "App - 问诊商品诊断搜索 Request VO")
@Data
@ToString(callSuper = true)
public class InquiryDiagnosisSearchRespDto implements Serializable {

    private Long id;

    @Schema(description = "商品编码")
    private String pref;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "诊断编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String diagnosisCode;

    @Schema(description = "诊断名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    private String diagnosisName;

    private String showName;

    @Schema(description = "诊断类型：0-默认(西医),1-中医", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String diagnosisType;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer rn;

    @Schema(description = "权重", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal weight;

}
