package com.xyy.saas.inquiry.pojo.migration;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class MigrationPrescriptionReqDto implements Serializable {

    /**
     * 最小id  >=
     */
    @NotNull
    private Long minId;

    /**
     * 最大id <
     */
    @NotNull
    private Long maxId;

    /**
     * 机构号
     */
    private String organSign;

}
