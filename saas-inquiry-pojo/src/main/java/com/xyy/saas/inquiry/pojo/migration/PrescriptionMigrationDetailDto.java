package com.xyy.saas.inquiry.pojo.migration;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2019/09/11
 * @Describe
 */
@Data
public class PrescriptionMigrationDetailDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 处方明细guid
     */
    private String guid;

    /**
     * 处方id;
     */
    private String prescriptionGuid;

    /**
     * 药品编码
     */
    private String productPref;

    /**
     * 药品通用名称
     */
    private String productName;

    /**
     * 商品名称
     */
    private String medicinesName;

    /**
     * 规格
     */
    private String attributeSpecification;

    /**
     * 单位
     */
    private Integer productUnitId;
    /**
     * 单次剂量单位
     */
    private String singleUnit;

    /**
     * 厂家
     */
    private String manufacturer;

    /**
     * 价格
     */
    private BigDecimal quantity;

    /**
     * 用法
     */
    private String directions;

    /**
     * 用量
     */
    private String singleDose;

    /**
     * 用量
     */
    private String singleDoseValue;

    /**
     * 频次
     */
    private String useFrequency;

    /**
     * 频次
     */
    private String useFrequencyDict;
    /**
     * 商品价格
     */
    private BigDecimal productPrice;
    /**
     * 实收金额
     */
    private BigDecimal actualAmount;
    /**
     * 包装单位
     */
    private String medicinesPackageUtil;

    /**
     * 国药准字号
     */
    private String medicinesQuasiName;


}