package com.xyy.saas.inquiry.aspect;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.security.core.LoginUser;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.alibaba.excel.util.StringUtils;
import com.xyy.saas.inquiry.annotation.InquiryDateType;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionDateTypeEnum;
import com.xyy.saas.inquiry.trace.service.TenantConfigApi;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.Date;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

@Aspect
@Component
@Slf4j
public class InquiryDateTypeAspect {

    // 使用延迟注入
    private final TenantConfigApi tenantConfigApi;

    @Autowired
    public InquiryDateTypeAspect(@Lazy TenantConfigApi tenantConfigApi) {
        this.tenantConfigApi = tenantConfigApi;
    }

    // 定义切点：拦截所有service
    @Around("@annotation(com.xyy.saas.inquiry.annotation.InquiryDateType)")
    public Object aroundMethods(ProceedingJoinPoint joinPoint) throws Throwable {
        // 执行原始方法
        Object result = joinPoint.proceed();
        try {
            return processResult(result);
        } catch (Exception e) {
            log.error("[InquiryDateTypeAspect]处理返回结果失败", e);
        }
        return result;
    }

    private Integer getTenantPresDateType() {

        // 超管取默认
        if (TenantContextHolder.getTenantId() == null || TenantConstant.isSystemTenant()) {
            return PrescriptionDateTypeEnum.YYYY_MM_DD_HH_MM_SS.getCode();
        }
        // 会员取默认
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser != null && ObjectUtil.equals(loginUser.getUserType(), UserTypeEnum.MEMBER.getValue())) {
            return PrescriptionDateTypeEnum.YYYY_MM_DD_HH_MM_SS.getCode();
        }

        return tenantConfigApi.getTenantPresDateType(TenantContextHolder.getRequiredTenantId());
    }

    private Object processResult(Object result) {
        if (result == null) {
            return null;
        }

        Integer dateType = getTenantPresDateType();

        // 处理CommonResult包装类型
        if (result instanceof CommonResult) {
            CommonResult<?> commonResult = (CommonResult<?>) result;
            Object data = commonResult.getData();
            if (data != null) {
                // 递归处理数据对象
                processObject(data, dateType);
            }
            return commonResult;
        }

        // 处理其他类型
        processObject(result, dateType);
        return result;
    }

    private void processObject(Object obj, Integer dateType) {
        if (obj == null) {
            return;
        }

        // 处理集合类型
        if (obj instanceof Collection) {
            for (Object item : (Collection<?>) obj) {
                processObject(item, dateType);
            }
            return;
        }

        // 处理数组类型
        if (obj.getClass().isArray()) {
            for (Object item : (Object[]) obj) {
                processObject(item, dateType);
            }
            return;
        }

        // 处理Map类型
        if (obj instanceof Map) {
            for (Object value : ((Map<?, ?>) obj).values()) {
                processObject(value, dateType);
            }
            return;
        }

        // 处理Map类型
        if (obj instanceof CommonResult<?>) {
            processObject(((CommonResult<?>) obj).getData(), dateType);
            return;
        }

        if (obj instanceof PageResult) {
            for (Object value : ((PageResult) obj).getList()) {
                processObject(value, dateType);
            }
            return;
        }

        // 处理自定义对象
        Class<?> clazz = obj.getClass();
        ReflectionUtils.doWithFields(clazz, field -> {
            InquiryDateType annotation = field.getAnnotation(InquiryDateType.class);
            if (annotation == null) {
                return;
            }
            String formattedDate = convertDateType(obj, annotation, dateType);
            if (StringUtils.isNotBlank(formattedDate)) {
                field.setAccessible(true);
                field.set(obj, formattedDate);
            }
        });
    }

    private String convertDateType(Object obj, InquiryDateType annotation, Integer dateType) {
        try {
            // 获取源字段的值
            Field sourceFiled = ReflectionUtils.findFieldIgnoreCase(obj.getClass(), annotation.value());
            if (sourceFiled == null) {
                return null;
            }
            sourceFiled.setAccessible(true);
            Object sourceValue = sourceFiled.get(obj);

            if (sourceValue == null) {
                return null;
            }

            // 处理LocalDateTime类型
            if (sourceValue instanceof LocalDateTime) {
                return PrescriptionDateTypeEnum.formatDate(dateType, (LocalDateTime) sourceValue);
            }

            if (sourceValue instanceof Date) {
                LocalDateTime dateTime = ((Date) sourceValue).toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
                return PrescriptionDateTypeEnum.formatDate(dateType, dateTime);
            }

            if (sourceValue instanceof String) {
                String dateTime = (String) sourceValue;
                try {
                    LocalDateTime parse = LocalDateTime.parse(dateTime, DateTimeFormatter.ofPattern(PrescriptionDateTypeEnum.YYYY_MM_DD_HH_MM_SS.getFormat()));
                    return PrescriptionDateTypeEnum.formatDate(dateType, parse);
                } catch (Exception e) {
                    return dateTime;
                }
            }
        } catch (Exception e) {
            log.warn("转换日期类型失败,annotation:{},e:{}", annotation, e.getMessage());
        }
        return null;
    }


}